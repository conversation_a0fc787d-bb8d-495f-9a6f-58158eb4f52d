import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SystemUser, SystemUserRole } from '@common/schemas';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class SwaggerAuthMiddleware implements NestMiddleware {
  constructor(
    @InjectModel(SystemUser.name)
    private readonly systemUserModel: Model<SystemUser>,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // Skip authentication for the Swagger JSON endpoint
    if (req.url.endsWith('-json')) {
      return next();
    }

    // Check for basic auth header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Basic ')) {
      return this.unauthorized(res);
    }

    // Verify credentials
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString(
      'utf-8',
    );
    const [username, password] = credentials.split(':');

    // Find user by username
    const user = await this.systemUserModel.findOne({ username }).exec();
    if (!user) {
      return this.unauthorized(res);
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return this.unauthorized(res);
    }

    if (user.role !== SystemUserRole.ADMIN) {
      return this.unauthorized(res);
    }

    // User is authenticated, proceed
    next();
  }

  private unauthorized(res: Response) {
    res.set('WWW-Authenticate', 'Basic realm="Swagger Documentation"');
    return res
      .status(401)
      .send('Authentication required for Swagger documentation');
  }
}

import { Injectable, LoggerService } from '@nestjs/common';
import { Logger, createLogger, transports } from 'winston';
import { LogtailTransport } from '@logtail/winston';
import { Logtail } from '@logtail/node';
import {
  LOGTAIL_HOST,
  LOGTAIL_SOURCE_TOKEN,
} from '@common/config/enviroment.config';

@Injectable()
export class AppLogger implements LoggerService {
  private context?: string;
  private winstonLogger: Logger;

  public setContext(context: string) {
    this.context = context;
  }

  constructor() {
    const logtail = new Logtail(LOGTAIL_SOURCE_TOKEN, {
      endpoint: LOGTAIL_HOST,
    });

    this.winstonLogger = createLogger({
      transports: [new transports.Console(), new LogtailTransport(logtail)],
    });
    this.context = 'APP';
  }

  log(message: any, context?: string) {
    return this.winstonLogger.info(message, {
      context: context || this.context,
    });
  }

  error(message: any, trace?: string, context?: string): any {
    return this.winstonLogger.error(message, {
      trace,
      context: context || this.context,
    });
  }

  warn(message: any, context?: string): any {
    return this.winstonLogger.warn(message, {
      context: context || this.context,
    });
  }

  debug(message: any, context?: string): any {
    return this.winstonLogger.debug(message, {
      context: context || this.context,
    });
  }

  verbose(message: any, context?: string): any {
    return this.winstonLogger.verbose(message, {
      context: context || this.context,
    });
  }
}

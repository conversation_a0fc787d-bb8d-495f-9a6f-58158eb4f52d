import { Request } from 'express';

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';

import { RefreshTokenService } from '@module/auth/services/refresh-token.service';

@Injectable()
export class RefreshJwtGuard implements CanActivate {
  constructor(private refreshTokenService: RefreshTokenService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: Request = context.switchToHttp().getRequest();
    const token = this.refreshTokenService.extractTokenFromHeader(request);

    if (!token) throw new UnauthorizedException();

    try {
      await this.refreshTokenService.verifyToken(token);
    } catch {
      throw new UnauthorizedException();
    }

    return true;
  }
}

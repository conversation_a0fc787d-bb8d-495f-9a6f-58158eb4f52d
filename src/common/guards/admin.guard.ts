import { AppException } from '@common/exceptions/app-exception';
import {
  USER_NOT_ADMIN,
  UN_AUTHORIZED,
  USER_NOT_EXIST,
} from '@common/exceptions/error';
import { SystemUser, SystemUserRole } from '@common/schemas';
import {
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    @InjectModel(SystemUser.name)
    private systemUserModel: Model<SystemUser>,
    private configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // TODO:: remove this
    if (request.headers['x-api-key'] === this.configService.get('X_API_KEY')) {
      return true;
    }

    if (!request.headers['authorization']) {
      throw new AppException(UN_AUTHORIZED);
    }
    const accessToken = request.headers['authorization'].split(' ')[1];

    const verifyToken = this.authenticationHandle(
      accessToken,
      this.configService.get('JWT_ACCESS_TOKEN_SECRET'),
    );
    const user = await this.systemUserModel
      .findOne({ id: verifyToken.userId })
      .exec();

    if (!user) {
      throw new AppException({
        ...USER_NOT_EXIST,
        statusCode: HttpStatus.UNAUTHORIZED,
      });
    }

    if (user.role !== SystemUserRole.ADMIN) {
      throw new AppException(USER_NOT_ADMIN);
    }

    request.user = user;

    return true;
  }

  private authenticationHandle(accessToken: string, secret: string) {
    try {
      return this.jwtService.verify(accessToken, {
        secret,
      });
    } catch (error) {
      throw new AppException(UN_AUTHORIZED);
    }
  }
}

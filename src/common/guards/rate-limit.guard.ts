import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RedisService } from '@shared/redis/redis.service';
import { AppLogger } from '@common/logger/logger.service';

export interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum number of requests per window
  keyGenerator?: (req: any) => string; // Custom key generator
}

export const RATE_LIMIT_KEY = 'rate_limit';

// Decorator to set rate limit options
export const RateLimit = (options: RateLimitOptions) => {
  return SetMetadata(RATE_LIMIT_KEY, options);
};

@Injectable()
export class RateLimitGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly redisService: RedisService,
    private readonly logger: AppLogger,
  ) {
    this.logger.setContext(RateLimitGuard.name);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const rateLimitOptions = this.reflector.getAllAndOverride<RateLimitOptions>(
      RATE_LIMIT_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!rateLimitOptions) {
      return true; // No rate limiting configured
    }

    const request = context.switchToHttp().getRequest();
    const key = this.generateKey(request, rateLimitOptions);

    try {
      const current = await this.redisService.get(key);
      const currentCount = current ? parseInt(current) : 0;

      if (currentCount >= rateLimitOptions.maxRequests) {
        this.logger.warn(
          `Rate limit exceeded for key: ${key}, count: ${currentCount}`,
        );
        throw new HttpException(
          {
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            message: 'Too many requests. Please try again later.',
            error: 'Rate Limit Exceeded',
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      // Increment counter
      const newCount = await this.redisService.incr(key);

      // Set expiration only on first request
      if (newCount === 1) {
        await this.redisService.expire(
          key,
          Math.ceil(rateLimitOptions.windowMs / 1000),
        );
      }

      this.logger.debug(
        `Rate limit check passed for key: ${key}, count: ${newCount}/${rateLimitOptions.maxRequests}`,
      );

      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Rate limit check failed: ${error.message}`);
      // On Redis error, allow the request to proceed
      return true;
    }
  }

  private generateKey(request: any, options: RateLimitOptions): string {
    if (options.keyGenerator) {
      return options.keyGenerator(request);
    }

    // Default key generation: use user ID if available, otherwise IP
    const userId = request.user?.id;
    const ip = request.ip || request.connection.remoteAddress;
    const endpoint = `${request.method}:${request.route?.path || request.url}`;

    return `rate_limit:${endpoint}:${userId || ip}`;
  }
}

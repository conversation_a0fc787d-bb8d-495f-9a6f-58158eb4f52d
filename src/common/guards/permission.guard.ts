import { AUTH_KEY } from '@common/decorators';
import { AppException } from '@common/exceptions/app-exception';
import { UN_AUTHORIZED, USER_NOT_EXIST } from '@common/exceptions/error';
import { SystemUser } from '@common/schemas';
import {
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    @InjectModel(SystemUser.name)
    private systemUserModel: Model<SystemUser>,
    private configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const requiredAuth = this.reflector.getAllAndOverride<boolean>(AUTH_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // handle authentication
    if (requiredAuth) {
      if (!request.headers['authorization']) {
        throw new AppException(UN_AUTHORIZED);
      }
      const accessToken = request.headers['authorization'].split(' ')[1];

      const verifyToken = this.authenticationHandle(
        accessToken,
        this.configService.get('JWT_ACCESS_TOKEN_SECRET'),
      );
      const user = await this.systemUserModel
        .findOne({ id: verifyToken.userId })
        .exec();

      if (!user) {
        throw new AppException({
          ...USER_NOT_EXIST,
          statusCode: HttpStatus.UNAUTHORIZED,
        });
      }

      request.user = user;
    }

    return true;
  }

  private authenticationHandle(accessToken: string, secret: string) {
    try {
      return this.jwtService.verify(accessToken, {
        secret,
      });
    } catch (error) {
      throw new AppException(UN_AUTHORIZED);
    }
  }

  //   private __handleException(type,error: any) {
}

import { AUTH_KEY } from '@common/decorators';
import { UN_AUTHORIZED, USER_NOT_EXIST } from '@common/exceptions/error';
import { SystemUser } from '@common/schemas';
import {
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { WsException } from '@nestjs/websockets';
import { Model } from 'mongoose';
import { Socket } from 'socket.io';

@Injectable()
export class WsPermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    @InjectModel(SystemUser.name)
    private systemUserModel: Model<SystemUser>,
    private configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const client: Socket = context.switchToWs().getClient<Socket>();

    const requiredAuth = this.reflector.getAllAndOverride<boolean>(AUTH_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (requiredAuth) {
      const authHeader = client.handshake.headers['authorization'];

      if (!authHeader) {
        console.log('No auth header');
        throw new WsException(UN_AUTHORIZED);
      }
      const accessToken = authHeader.split(' ')[1];

      const verifyToken = this.authenticationHandle(
        accessToken,
        this.configService.get('JWT_ACCESS_TOKEN_SECRET'),
      );

      const user = await this.systemUserModel
        .findOne({ id: verifyToken.userId })
        .exec();

      if (!user) {
        throw new WsException({
          ...USER_NOT_EXIST,
          statusCode: HttpStatus.UNAUTHORIZED,
        });
      }

      context.switchToWs().getData().user = user;
    }

    return true;
  }

  private authenticationHandle(accessToken: string, secret: string) {
    try {
      return this.jwtService.verify(accessToken, {
        secret,
      });
    } catch (error) {
      throw new WsException(UN_AUTHORIZED);
    }
  }
}

import { HttpStatus } from '@nestjs/common';
import { IAppError } from './app-error.interface';

export const FORBIDDEN: IAppError = {
  errorCode: 'FORBIDDEN',
  errorMessage: 'Not access to resource',
  statusCode: HttpStatus.FORBIDDEN,
};

export const LIVE_NOT_EXIST: IAppError = {
  errorCode: 'LIVE_NOT_EXIST',
  errorMessage: 'Live session not exist',
  statusCode: HttpStatus.FORBIDDEN,
};

export const USER_NOT_EXIST: IAppError = {
  errorCode: 'USER_NOT_EXIST',
  errorMessage: 'User not exist',
  statusCode: HttpStatus.NOT_FOUND,
};

export const USER_ALREADY_EXIST: IAppError = {
  errorCode: 'USER_ALREADY_EXIST',
  errorMessage: 'User already exist',
  statusCode: HttpStatus.CONFLICT,
};

export const PASSWORD_NOT_CORRECT: IAppError = {
  errorCode: 'PASSWORD_NOT_CORRECT',
  errorMessage: 'Password not correct',
  statusCode: HttpStatus.BAD_REQUEST,
};

export const UN_AUTHORIZED: IAppError = {
  errorCode: 'UN_AUTHORIZED',
  errorMessage: 'Unauthorized',
  statusCode: HttpStatus.UNAUTHORIZED,
};

export const PLATFORM_NOT_SUPPORT: IAppError = {
  errorCode: 'PLATFORM_NOT_SUPPORT',
  errorMessage: 'Platform not support',
  statusCode: HttpStatus.BAD_REQUEST,
};

export const LIVE_CHANNEL_NOT_EXIST: IAppError = {
  errorCode: 'LIVE_CHANNEL_NOT_EXIST',
  errorMessage: 'Live channel not exist',
  statusCode: HttpStatus.NOT_FOUND,
};

export const USER_NOT_ADMIN: IAppError = {
  errorCode: 'USER_NOT_ADMIN',
  errorMessage: 'User not admin',
  statusCode: HttpStatus.FORBIDDEN,
};

export const PLATFORM_CHANNEL_NOT_EXIST: IAppError = {
  errorCode: 'PLATFORM_CHANNEL_NOT_EXIST',
  errorMessage: 'Platform channel not exist',
  statusCode: HttpStatus.NOT_FOUND,
};

export const SESSION_INACTIVE: IAppError = {
  errorCode: 'SESSION_INACTIVE',
  errorMessage: 'Your session has ended due to inactivity',
  statusCode: HttpStatus.GONE,
};

export const STREAMING_LIMIT_REACHED: IAppError = {
  errorCode: 'STREAMING_LIMIT_REACHED',
  errorMessage: 'You have reached your daily streaming limit',
  statusCode: HttpStatus.FORBIDDEN,
};

export const PLATFORM_CHANNEL_ALREADY_STREAMING: IAppError = {
  errorCode: 'PLATFORM_CHANNEL_ALREADY_STREAMING',
  errorMessage: 'This platform channel is already streaming',
  statusCode: HttpStatus.CONFLICT,
};

export const CONCURRENT_SESSIONS_LIMIT_REACHED: IAppError = {
  errorCode: 'CONCURRENT_SESSIONS_LIMIT_REACHED',
  errorMessage: 'You have reached your concurrent live sessions limit',
  statusCode: HttpStatus.FORBIDDEN,
};

export const INVALID_PLATFORM_CHANNEL_ID: IAppError = {
  errorCode: 'INVALID_PLATFORM_CHANNEL_ID',
  errorMessage: 'Platform channel ID is required',
  statusCode: HttpStatus.BAD_REQUEST,
};

export const ACCOUNT_DELETION_ERROR: IAppError = {
  errorCode: 'ACCOUNT_DELETION_ERROR',
  errorMessage: 'Error deleting account',
  statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
};

export const TRIAL_DAYS_ENDED: IAppError = {
  errorCode: 'TRIAL_DAYS_ENDED',
  errorMessage: 'Trial days have ended',
  statusCode: HttpStatus.FORBIDDEN,
};

export const START_LIVE_SESSION_FAILED: IAppError = {
  errorCode: 'START_LIVE_SESSION_FAILED',
  errorMessage: 'Failed to start live session',
  statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
};

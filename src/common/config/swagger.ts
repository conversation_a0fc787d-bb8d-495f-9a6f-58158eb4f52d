import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ENV } from '@common/config';
import { SwaggerAuthMiddleware } from '@common/middlewares/swagger-auth.middleware';
import { SystemUser, SystemUserSchema } from '@common/schemas';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';

const config = new DocumentBuilder()
  .addServer(ENV.SERVER_API)
  .setTitle('Live comments')
  .setDescription('api')
  .setVersion('1.0')
  .addBearerAuth()
  .build();

export const initSwagger = (app: NestExpressApplication) => {
  const document = SwaggerModule.createDocument(app, config);

  // Get the SystemUser model from the app container
  const systemUserModel = app.get<Model<SystemUser>>(
    getModelToken(SystemUser.name),
  );

  // Create a middleware instance with the model
  const swaggerAuthMiddleware = new SwaggerAuthMiddleware(systemUserModel);

  // Apply the middleware to the Swagger routes
  app.use('/docs', (req, res, next) =>
    swaggerAuthMiddleware.use(req, res, next),
  );

  SwaggerModule.setup('docs', app, document);
};

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const dbUrl = configService.get('DATABASE_URL');
        const dbName = configService.get('DATABASE_NAME');
        const uri = configService.get('DATABASE_URI') || `${dbUrl}/${dbName}`;
        return {
          uri: uri,
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [],
})
export class DatabaseModule {}

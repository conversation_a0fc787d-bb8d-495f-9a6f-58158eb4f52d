import * as dotenv from 'dotenv';
dotenv.config();

const NODE_ENV: string = process.env.NODE_ENV || 'development';
const SERVER_API: string = process.env.SERVER_API || 'http://localhost:3000';
const SIGN_SERVER: string = process.env.SIGN_SERVER || 'http://localhost:8080/';
const HEADLESS_SERVER: string =
  process.env.HEADLESS_SERVER || 'http://localhost:4005';
const DATABASE_CONFIG = {
  url: process.env.DATABASE_URL || 'mongodb://localhost:27017',
  name: process.env.DATABASE_NAME || 'livestream-db',
};
const JWT_CONFIG = {
  secret: process.env.JWT_ACCESS_TOKEN_SECRET || 'OxsuVQwpERrY',
  expiresIn: process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME || '7d',
};
const LOGTAIL_SOURCE_TOKEN: string = process.env.LOGTAIL_SOURCE_TOKEN || '';
const LOGTAIL_HOST: string = process.env.LOGTAIL_HOST || '';

export {
  NODE_ENV,
  SERVER_API,
  SIGN_SERVER,
  DATABASE_CONFIG,
  JWT_CONFIG,
  HEADLESS_SERVER,
  LOGTAIL_SOURCE_TOKEN,
  LOGTAIL_HOST,
};

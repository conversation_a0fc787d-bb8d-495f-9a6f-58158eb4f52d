export const isValidHttpUrl = (text: string) => {
  let url: URL;

  try {
    url = new URL(text);
  } catch (_) {
    return false;
  }

  return url.protocol === 'http:' || url.protocol === 'https:';
};

export const ensureTrailingSlash = (url: string) => {
  if (!url.endsWith('/')) {
    return url + '/';
  }
  return url;
};

export const removeTrailingSlash = (url: string) => {
  if (url.endsWith('/')) {
    return url.slice(0, -1);
  }
  return url;
};

import { Request } from 'express';

import { ExecutionContext, createParamDecorator } from '@nestjs/common';

export const Token = createParamDecorator(
  (
    data: 'accessToken' | 'refreshToken',
    ctx: ExecutionContext,
  ): string | undefined => {
    const request: Request = ctx.switchToHttp().getRequest();

    const [type, token] = request.headers.authorization?.split(' ') ?? [];

    if (data === 'refreshToken') {
      return type === 'Refresh' ? token : null;
    }

    return type === 'Bearer' ? token : null;
  },
);

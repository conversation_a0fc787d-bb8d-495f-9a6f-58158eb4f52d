// src/user/user.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { SubscriptionDocument } from '@common/schemas/subscription.schema';

export interface SystemUserDocument extends Document {
  id: string;
  username: string;
  email: string;
  password: string;
  role: SystemUserRole;
  subscriptionId?: string;
  subscription?: SubscriptionDocument;
  createdAt: Date;
  updatedAt: Date;
  activeAt: Date;
}

export enum SystemUserRole {
  USER = 'user',
  ADMIN = 'admin',
}

@Schema()
export class SystemUser {
  @Prop({ default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ required: true, unique: true })
  username: string;

  @Prop({ required: false, unique: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ type: String, enum: SystemUserRole, default: SystemUserRole.USER })
  role: SystemUserRole;

  @Prop({
    type: String,
    required: false,
    index: true,
  })
  subscriptionId?: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;

  @Prop({ type: Date, default: Date.now })
  activeAt: Date;
}

export const SystemUserSchema = SchemaFactory.createForClass(SystemUser);

SystemUserSchema.virtual('subscription', {
  ref: 'Subscription',
  localField: 'subscriptionId',
  foreignField: 'id',
  justOne: true,
});

SystemUserSchema.set('toJSON', { virtuals: true });
SystemUserSchema.set('toObject', { virtuals: true });

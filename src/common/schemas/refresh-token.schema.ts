import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

export type RefreshTokenDocument = HydratedDocument<RefreshToken>;

@Schema()
export class RefreshToken {
  @Prop({ type: String, default: () => uuidv4(), index: true, unique: true })
  id: string;

  @Prop({ type: String, index: true })
  token: string;

  @Prop({ type: String })
  currentToken: string;

  @Prop({ type: Boolean, default: false })
  isRevoked: boolean;

  @Prop({ type: Date })
  revokedAt: Date;

  @Prop({ type: String, ref: 'SystemUser' })
  userId: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const RefreshTokenSchema = SchemaFactory.createForClass(RefreshToken);

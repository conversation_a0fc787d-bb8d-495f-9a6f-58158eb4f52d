import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { LivestreamPlatform } from '@common/constants';
import { v4 as uuidv4 } from 'uuid';

export type PlatformUserDocument = HydratedDocument<PlatformUser>;

@Schema()
export class PlatformUser {
  @Prop({ type: String, default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ type: String, index: true })
  userId: string;

  @Prop({ type: String })
  secUid: string;

  @Prop({ type: String, index: true })
  uniqueId: string;

  @Prop({ type: String })
  nickname: string;

  @Prop({ type: Boolean, default: false })
  hasCanceledOrder?: boolean;

  @Prop({ type: String, enum: LivestreamPlatform })
  livestreamPlatform: LivestreamPlatform;

  @Prop({ type: String, required: false })
  profilePictureUrl?: string;

  @Prop({ type: String, required: false, index: true })
  phoneNumber?: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

export const PlatformUserSchema = SchemaFactory.createForClass(PlatformUser);

PlatformUserSchema.index(
  { userId: 1, livestreamPlatform: 1 },
  { unique: true },
);

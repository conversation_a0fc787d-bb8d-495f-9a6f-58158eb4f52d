import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { LivestreamPlatform } from '@common/constants';

@Schema()
export class PlatformChannel {
  @Prop({ type: String, default: () => uuidv4(), index: true, unique: true })
  id: string;

  @Prop({ type: String, index: true })
  channelId: string;

  @Prop({ type: String })
  recognizeId: string;

  @Prop({ type: String, default: '' })
  name: string;

  @Prop({ type: String, enum: LivestreamPlatform })
  livestreamPlatform: LivestreamPlatform;

  @Prop({ type: String, ref: 'SystemUser' })
  createdBy: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export type PlatformChannelDocument = PlatformChannel & Document;

export const PlatformChannelSchema =
  SchemaFactory.createForClass(PlatformChannel);

PlatformChannelSchema.index(
  {
    livestreamPlatform: 1,
    channelId: 1,
    createdBy: 1,
  },
  { unique: true },
);

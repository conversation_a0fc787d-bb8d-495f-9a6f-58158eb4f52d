import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

export interface SubscriptionDocument extends Document {
  id: string;
  name: string;
  description?: string;
  plan: SubscriptionPlan;
  privileges: SubscriptionPrivilege[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum SubscriptionPlan {
  FREE = 'free',
  BASIC = 'basic',
  PREMIUM = 'premium',
}

export enum SubscriptionPrivilegeKey {
  LIVE_STREAM_DURATION = 'live_stream_duration',
  CONCURRENT_LIVE_SESSIONS = 'concurrent_live_sessions',
  PLATFORM_CHANNELS = 'platform_channels',
  TRIAL_DAYS = 'trial_days',
}

export interface SubscriptionPrivilege {
  key: SubscriptionPrivilegeKey;
  limit: number | null;
  unlimited: boolean;
}

@Schema({ timestamps: true })
export class Subscription {
  @Prop({ default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description?: string;

  @Prop({
    type: String,
    enum: SubscriptionPlan,
    default: SubscriptionPlan.FREE,
    unique: true,
  })
  plan: SubscriptionPlan;

  @Prop({ type: Object, default: [] })
  privileges: SubscriptionPrivilege[];

  @Prop({ type: String, ref: 'SystemUser' })
  createdBy: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const SubscriptionSchema = SchemaFactory.createForClass(Subscription);

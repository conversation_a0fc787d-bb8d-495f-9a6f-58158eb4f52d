import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

export type FacebookAccountDocument = FacebookAccount & Document;

export enum FacebookAccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Schema()
export class FacebookAccount {
  @Prop({ type: String, default: () => uuidv4(), index: true, unique: true })
  id: string;

  @Prop({ type: String, index: true })
  fid: string;

  @Prop({ type: String, enum: FacebookAccountStatus })
  status: FacebookAccountStatus;

  @Prop({ type: String })
  fullName: string;

  @Prop({ type: String })
  avatar: string;

  @Prop({ type: [Object], default: [] })
  cookies: any[];

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const FacebookAccountSchema =
  SchemaFactory.createForClass(FacebookAccount);

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class SystemUserPlatformChannel {
  @Prop({ type: String, ref: 'SystemUser', required: true })
  systemUser: string;

  @Prop({ type: String, ref: 'PlatformChannel', required: true })
  platformChannel: string;
}

export type SystemUserPlatformChannelDocument = SystemUserPlatformChannel &
  Document;

export const SystemUserPlatformChannelSchema = SchemaFactory.createForClass(
  SystemUserPlatformChannel,
);

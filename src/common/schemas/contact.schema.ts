import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

export interface ContactDocument extends Document {
  id: string;
  name?: string;
  email?: string;
  phoneNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

@Schema({ timestamps: true })
export class Contact {
  @Prop({ default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ required: false, type: String })
  name?: string;

  @Prop({ required: false, type: String })
  email?: string;

  @Prop({ required: false, type: String })
  phoneNumber?: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const ContactSchema = SchemaFactory.createForClass(Contact);

// Create index for chronological queries
ContactSchema.index({ createdAt: -1 });

// Create text index for search functionality
ContactSchema.index({
  name: 'text',
  email: 'text',
  phoneNumber: 'text',
});

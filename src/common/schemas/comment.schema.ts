import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument, Types } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { PlatformUser } from './platform-user.schema';
import { LiveSession } from '@common/schemas/live-session.schema';

export type CommentDocument = Comment &
  Document & {
    platformUser?: PlatformUser;
    liveSession?: LiveSession;
  };

@Schema()
export class Comment {
  @Prop({ type: String, default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ type: String, required: true, index: true })
  commentId: string;

  @Prop({ type: String })
  message: string;

  @Prop({ type: String, index: true })
  liveSessionId: string;

  @Prop({ type: String, index: true })
  roomId: string;

  @Prop({ type: String, required: false })
  platformUserId?: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

export const CommentSchema = SchemaFactory.createForClass(Comment);

CommentSchema.virtual('liveSession', {
  ref: 'LiveSession',
  localField: 'liveSessionId',
  foreignField: 'id',
  justOne: true,
});

CommentSchema.virtual('platformUser', {
  ref: 'PlatformUser',
  localField: 'platformUserId',
  foreignField: 'id',
  justOne: true,
});

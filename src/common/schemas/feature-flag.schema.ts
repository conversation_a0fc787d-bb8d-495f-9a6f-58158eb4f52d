import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

export interface FeatureFlagDocument extends Document {
  id: string;
  name: string;
  description?: string;
  key: string;
  isActive: boolean;
  restrictedVersions: string[];
  createdAt: Date;
  updatedAt: Date;
}

@Schema({ timestamps: true })
export class FeatureFlag {
  @Prop({ default: uuidv4, index: true, unique: true })
  id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ required: true, index: true, unique: true })
  key: string;

  @Prop({ type: Boolean, default: false })
  isActive: boolean;

  @Prop({ type: [String], index: true })
  restrictedVersions: string[];

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const FeatureFlagSchema = SchemaFactory.createForClass(FeatureFlag);

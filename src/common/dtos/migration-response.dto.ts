import { ApiProperty } from '@nestjs/swagger';

/**
 * Standard response DTO for migration operations
 * Can be used across different modules for various migration tasks
 */
export class MigrationResponseDto {
  @ApiProperty({ description: 'Whether the migration was successful' })
  success: boolean;

  @ApiProperty({ description: 'Summary message about the migration' })
  message: string;

  @ApiProperty({ description: 'Number of items successfully migrated' })
  migratedCount: number;

  @ApiProperty({
    description: 'IDs of successfully migrated items',
    type: [String],
  })
  migratedIds: string[];

  @ApiProperty({ description: 'Total number of items processed' })
  totalProcessed: number;

  @ApiProperty({ description: 'Number of items skipped during migration' })
  skippedCount: number;

  @ApiProperty({
    description: 'List of error messages encountered during migration',
    type: [String],
  })
  errors: string[];
}

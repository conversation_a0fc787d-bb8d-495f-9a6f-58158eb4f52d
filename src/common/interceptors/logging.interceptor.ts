import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { AppLogger } from '../logger/logger.service';
import { REQUEST_ID_TOKEN_HEADER } from '../constants';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private appLogger: AppLogger) {
    this.appLogger.setContext(LoggingInterceptor.name);
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const method = request.method;
    const url = request.originalUrl;
    const requestID = request.headers[REQUEST_ID_TOKEN_HEADER];

    if (url === '/api/health') {
      return next.handle();
    }
    this.appLogger.log({ method, requestID, url });

    const now = Date.now();
    return next.handle().pipe(
      tap(() => {
        const response = context.switchToHttp().getResponse();
        const statusCode = response.statusCode;

        const responseTime = Date.now() - now;
        this.appLogger.log({
          method,
          requestID,
          url,
          statusCode,
          responseTime,
        });
      }),
    );
  }
}

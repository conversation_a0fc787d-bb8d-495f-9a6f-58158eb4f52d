import { CustomConfigModule } from '@common/config/config.module';
import { LoggerModule } from '@common/logger/logger.module';
import { ApiModule } from '@module/api.module';
import { Module } from '@nestjs/common';
import { OpenTelemetryModule } from 'nestjs-otel';

@Module({
  imports: [
    CustomConfigModule,
    ApiModule,
    LoggerModule,
    OpenTelemetryModule.forRoot({
      metrics: {
        hostMetrics: true,
        apiMetrics: {
          enable: true,
          ignoreUndefinedRoutes: false,
        },
      },
    }),
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}

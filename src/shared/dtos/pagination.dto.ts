import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsIn } from 'class-validator';

export class PaginationOptionsDto {
  @ApiProperty({ required: false, default: 1 })
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @ApiProperty({ required: false, default: 10 })
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  @ApiProperty({ required: false, default: '-createdAt' })
  sort?: string;
}

export class PaginationMetaDto {
  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  pages: number;
}

export class PaginationResultDto<T> {
  @ApiProperty({ isArray: true })
  data: T[];

  @ApiProperty()
  meta: PaginationMetaDto;

  constructor(data: T[], meta: PaginationMetaDto) {
    this.data = data;
    this.meta = meta;
  }
}

// Cursor-based pagination DTOs
export class CursorPaginationOptionsDto {
  @ApiProperty({
    required: false,
    description:
      'Base64 encoded cursor for pagination. Use nextCursor from previous response for next page, or prevCursor for previous page.',
  })
  @IsOptional()
  @IsString()
  cursor?: string;

  @ApiProperty({
    required: false,
    default: 10,
    description: 'Number of items to return (1-100)',
  })
  @Transform(({ value }) => {
    const num = parseInt(value);
    return Math.min(Math.max(num, 1), 100); // Clamp between 1 and 100
  })
  limit?: number;

  @ApiProperty({
    required: false,
    default: 'next',
    enum: ['next', 'prev'],
    description:
      'Direction of pagination. Use "next" for forward pagination, "prev" for backward pagination.',
  })
  @IsOptional()
  @IsIn(['next', 'prev'])
  direction?: 'next' | 'prev';
}

export class CursorPaginationMetaDto {
  @ApiProperty({
    description:
      'Base64 encoded cursor for the next page. Null if no more items.',
  })
  nextCursor: string | null;

  @ApiProperty({
    description:
      'Base64 encoded cursor for the previous page. Null if this is the first page.',
  })
  prevCursor: string | null;

  @ApiProperty({
    description: 'Whether there are more items after the current page',
  })
  hasNext: boolean;

  @ApiProperty({
    description: 'Whether there are more items before the current page',
  })
  hasPrevious: boolean;

  @ApiProperty({
    description: 'Number of items returned in this page',
  })
  count: number;

  @ApiProperty({
    description: 'Number of items requested (limit)',
  })
  limit: number;
}

export class CursorPaginationResultDto<T> {
  @ApiProperty({ isArray: true })
  data: T[];

  @ApiProperty()
  meta: CursorPaginationMetaDto;

  constructor(data: T[], meta: CursorPaginationMetaDto) {
    this.data = data;
    this.meta = meta;
  }
}

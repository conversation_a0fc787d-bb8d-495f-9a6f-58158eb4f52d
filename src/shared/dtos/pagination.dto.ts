import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class PaginationOptionsDto {
  @ApiProperty({ required: false, default: 1 })
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @ApiProperty({ required: false, default: 10 })
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  @ApiProperty({ required: false, default: '-createdAt' })
  sort?: string;
}

export class PaginationMetaDto {
  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  pages: number;
}

export class PaginationResultDto<T> {
  @ApiProperty({ isArray: true })
  data: T[];

  @ApiProperty()
  meta: PaginationMetaDto;

  constructor(data: T[], meta: PaginationMetaDto) {
    this.data = data;
    this.meta = meta;
  }
}

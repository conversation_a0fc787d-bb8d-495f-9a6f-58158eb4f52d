import { DynamicModule, Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisService } from './redis.service';
import { RedisConnectionManagerService } from './redis-connection-manager.service';
import { LoggerModule } from '@common/logger/logger.module';
import { AppLogger } from '@common/logger/logger.service';

@Global()
@Module({})
export class RedisModule {
  static forRoot(): DynamicModule {
    return {
      module: RedisModule,
      imports: [ConfigModule, LoggerModule],
      providers: [RedisConnectionManagerService, RedisService, AppLogger],
      exports: [RedisService, RedisConnectionManagerService],
    };
  }

  static forRootAsync(options: {
    imports?: any[];
    inject?: any[];
    useFactory?: (...args: any[]) => any;
  }): DynamicModule {
    const imports = options.imports || [ConfigModule, LoggerModule];

    // We're ignoring useFactory and inject as we don't need them anymore
    // This is just for backward compatibility

    return {
      module: RedisModule,
      imports,
      providers: [RedisConnectionManagerService, RedisService, AppLogger],
      exports: [RedisService, RedisConnectionManagerService],
    };
  }
}

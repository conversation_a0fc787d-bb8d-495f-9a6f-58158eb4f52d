import { Injectable, OnM<PERSON>ule<PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { Redis } from 'ioredis';
import { AppLogger } from '@common/logger/logger.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RedisConnectionManagerService
  implements OnModuleInit, OnModuleDestroy
{
  private mainClient: Redis;
  private pubClient: Redis;
  private subClient: Redis;
  private clientPool: Map<string, Redis> = new Map();
  private isShuttingDown = false;

  constructor(
    private readonly logger: AppLogger,
    private readonly configService: ConfigService,
  ) {
    this.logger.setContext(RedisConnectionManagerService.name);
    this.initializeConnections();
  }

  async onModuleInit() {
    // await this.initializeConnections();
  }

  async onModuleDestroy() {
    this.isShuttingDown = true;
    await this.closeAllConnections();
  }

  private async initializeConnections() {
    try {
      // Create main client
      this.mainClient = this.createClient('main');

      // Create pub/sub clients
      this.pubClient = this.createClient('pub');
      this.subClient = this.createClient('sub');

      this.logger.log('Redis connections initialized successfully');
    } catch (error) {
      this.logger.error(
        `Failed to initialize Redis connections: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private createClient(name: string): Redis {
    const redisOptions = {
      host: this.configService.get('REDIS_HOST', 'localhost'),
      port: this.configService.get('REDIS_PORT', 6379),
      password: this.configService.get('REDIS_PASSWORD', ''),
      db: this.configService.get('REDIS_DB', 0),
      retryStrategy: (times: number) => {
        if (this.isShuttingDown) return null; // Don't retry during shutdown

        const delay = Math.min(times * 50, 2000);
        this.logger.log(
          `Retrying Redis connection in ${delay}ms (attempt ${times})`,
        );
        return delay;
      },
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
    };

    const client = new Redis(redisOptions);

    // Set up event handlers
    client.on('connect', () => {
      this.logger.log(`Redis client '${name}' connected`);
    });

    client.on('ready', () => {
      this.logger.log(`Redis client '${name}' ready`);
    });

    client.on('error', err => {
      this.logger.error(
        `Redis client '${name}' error: ${err.message}`,
        err.stack,
      );
    });

    client.on('close', () => {
      this.logger.log(`Redis client '${name}' connection closed`);
    });

    client.on('reconnecting', () => {
      this.logger.log(`Redis client '${name}' reconnecting...`);
    });

    // Store in pool
    this.clientPool.set(name, client);

    return client;
  }

  getClient(): Redis {
    return this.mainClient;
  }

  getPubClient(): Redis {
    return this.pubClient;
  }

  getSubClient(): Redis {
    return this.subClient;
  }

  // Create a new client for specific use cases
  createNamedClient(name: string): Redis {
    if (this.clientPool.has(name)) {
      return this.clientPool.get(name);
    }

    const client = this.createClient(name);
    return client;
  }

  // Get a client by name (returns null if not found)
  getNamedClient(name: string): Redis | null {
    return this.clientPool.get(name) || null;
  }

  // Close a specific client
  async closeClient(name: string): Promise<void> {
    const client = this.clientPool.get(name);
    if (client) {
      await client.quit();
      this.clientPool.delete(name);
      this.logger.log(`Redis client '${name}' closed and removed from pool`);
    }
  }

  // Close all connections
  async closeAllConnections(): Promise<void> {
    this.logger.log('Closing all Redis connections...');

    // Convert entries to array and explicitly type it to ensure proper iteration
    const entries = Array.from(this.clientPool.entries()) as [string, Redis][];

    const closePromises = entries.map(async entry => {
      const name = entry[0];
      const client = entry[1];
      try {
        await client.quit();
        this.logger.log(`Redis client '${name}' closed successfully`);
      } catch (error) {
        this.logger.error(
          `Error closing Redis client '${name}': ${error.message}`,
        );
      }
    });

    await Promise.all(closePromises);
    this.clientPool.clear();
    this.logger.log('All Redis connections closed');
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.mainClient.ping();
      return result === 'PONG';
    } catch (error) {
      this.logger.error(`Redis health check failed: ${error.message}`);
      return false;
    }
  }
}

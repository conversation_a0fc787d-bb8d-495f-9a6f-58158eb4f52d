import { Injectable } from '@nestjs/common';
import { <PERSON>is, ChainableCommander } from 'ioredis';
import { AppLogger } from '@common/logger/logger.service';
import { RedisConnectionManagerService } from './redis-connection-manager.service';

@Injectable()
export class RedisService {
  constructor(
    private readonly connectionManager: RedisConnectionManagerService,
    private readonly logger: AppLogger,
  ) {
    this.logger.setContext(RedisService.name);
  }

  getClient(): Redis {
    return this.connectionManager.getClient();
  }

  getPubClient(): Redis {
    return this.connectionManager.getPubClient();
  }

  getSubClient(): Redis {
    return this.connectionManager.getSubClient();
  }

  createNamedClient(name: string): Redis {
    return this.connectionManager.createNamedClient(name);
  }

  // Key-value operations
  async get(key: string): Promise<string | null> {
    try {
      return await this.getClient().get(key);
    } catch (error) {
      this.logger.error(`Error getting key ${key}: ${error.message}`);
      throw error;
    }
  }

  async set(key: string, value: string | number, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.getClient().set(key, value, 'EX', ttl);
      } else {
        await this.getClient().set(key, value);
      }
    } catch (error) {
      this.logger.error(`Error setting key ${key}: ${error.message}`);
      throw error;
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.getClient().del(key);
    } catch (error) {
      this.logger.error(`Error deleting key ${key}: ${error.message}`);
      throw error;
    }
  }

  // Counter operations
  async incr(key: string): Promise<number> {
    try {
      return await this.getClient().incr(key);
    } catch (error) {
      this.logger.error(`Error incrementing key ${key}: ${error.message}`);
      throw error;
    }
  }

  async incrby(key: string, increment: number): Promise<number> {
    try {
      return await this.getClient().incrby(key, increment);
    } catch (error) {
      this.logger.error(
        `Error incrementing key ${key} by ${increment}: ${error.message}`,
      );
      throw error;
    }
  }

  async decr(key: string): Promise<number> {
    try {
      return await this.getClient().decr(key);
    } catch (error) {
      this.logger.error(`Error decrementing key ${key}: ${error.message}`);
      throw error;
    }
  }

  async decrby(key: string, decrement: number): Promise<number> {
    try {
      return await this.getClient().decrby(key, decrement);
    } catch (error) {
      this.logger.error(
        `Error decrementing key ${key} by ${decrement}: ${error.message}`,
      );
      throw error;
    }
  }

  // Expiration
  async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.getClient().expire(key, seconds);
    } catch (error) {
      this.logger.error(
        `Error setting expiration for key ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.getClient().ttl(key);
    } catch (error) {
      this.logger.error(`Error getting TTL for key ${key}: ${error.message}`);
      throw error;
    }
  }

  // Set operations
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.getClient().sadd(key, ...members);
    } catch (error) {
      this.logger.error(`Error adding members to set ${key}: ${error.message}`);
      throw error;
    }
  }

  async srem(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.getClient().srem(key, ...members);
    } catch (error) {
      this.logger.error(
        `Error removing members from set ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async smembers(key: string): Promise<string[]> {
    try {
      return await this.getClient().smembers(key);
    } catch (error) {
      this.logger.error(
        `Error getting members of set ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async scard(key: string): Promise<number> {
    try {
      return await this.getClient().scard(key);
    } catch (error) {
      this.logger.error(
        `Error getting cardinality of set ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async sismember(key: string, member: string): Promise<number> {
    try {
      return await this.getClient().sismember(key, member);
    } catch (error) {
      this.logger.error(
        `Error checking if member exists in set ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  // Hash operations
  async hset(
    key: string,
    field: string,
    value: string | number,
  ): Promise<number> {
    try {
      return await this.getClient().hset(key, field, value);
    } catch (error) {
      this.logger.error(
        `Error setting hash field ${field} in key ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async hget(key: string, field: string): Promise<string | null> {
    try {
      return await this.getClient().hget(key, field);
    } catch (error) {
      this.logger.error(
        `Error getting hash field ${field} from key ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async hdel(key: string, ...fields: string[]): Promise<number> {
    try {
      return await this.getClient().hdel(key, ...fields);
    } catch (error) {
      this.logger.error(
        `Error deleting hash fields from key ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    try {
      return await this.getClient().hgetall(key);
    } catch (error) {
      this.logger.error(
        `Error getting all hash fields from key ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  // Utility methods
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.getClient().keys(pattern);
    } catch (error) {
      this.logger.error(
        `Error getting keys with pattern ${pattern}: ${error.message}`,
      );
      throw error;
    }
  }

  // Transaction support
  async multi(): Promise<ChainableCommander> {
    try {
      return this.getClient().multi();
    } catch (error) {
      this.logger.error(`Error creating multi command: ${error.message}`);
      throw error;
    }
  }

  // Lua script execution for atomic operations
  async eval(
    script: string,
    keys: string[],
    args: (string | number)[],
  ): Promise<any> {
    try {
      return await this.getClient().eval(script, keys.length, ...keys, ...args);
    } catch (error) {
      this.logger.error(`Error executing Lua script: ${error.message}`);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    return this.connectionManager.healthCheck();
  }
}

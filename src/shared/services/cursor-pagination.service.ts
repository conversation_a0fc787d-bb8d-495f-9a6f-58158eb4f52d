import { Injectable } from '@nestjs/common';
import { Model, Document, FilterQuery } from 'mongoose';
import {
  CursorPaginationOptionsDto,
  CursorPaginationResultDto,
  CursorPaginationMetaDto,
} from '../dtos/pagination.dto';

export interface CursorPaginationConfig {
  cursorField: string; // Field to use for cursor (e.g., 'createdAt', '_id')
  sortOrder: 'asc' | 'desc'; // Sort order for the cursor field
  populateFields?: string[]; // Fields to populate in the query
}

export interface DecodedCursor {
  value: any;
  field: string;
}

@Injectable()
export class CursorPaginationService {
  /**
   * Encode a cursor value to base64
   */
  private encodeCursor(value: any, field: string): string {
    const cursorData = { value, field };
    return Buffer.from(JSON.stringify(cursorData)).toString('base64');
  }

  /**
   * Decode a base64 cursor to its original value
   */
  private decodeCursor(cursor: string): DecodedCursor | null {
    try {
      const decoded = Buffer.from(cursor, 'base64').toString('utf-8');
      const cursorData = JSON.parse(decoded);
      return {
        value: cursorData.value,
        field: cursorData.field,
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Build MongoDB query based on cursor and direction
   */
  private buildCursorQuery<T extends Document>(
    baseQuery: FilterQuery<T>,
    cursor: string | undefined,
    direction: 'next' | 'prev',
    config: CursorPaginationConfig,
  ): FilterQuery<T> {
    const query: any = { ...baseQuery };

    if (!cursor) {
      return query;
    }

    const decodedCursor = this.decodeCursor(cursor);
    if (!decodedCursor || decodedCursor.field !== config.cursorField) {
      return query;
    }

    const { value } = decodedCursor;
    const { cursorField, sortOrder } = config;

    // Determine comparison operator based on direction and sort order
    let operator: string;
    if (direction === 'next') {
      operator = sortOrder === 'desc' ? '$lt' : '$gt';
    } else {
      operator = sortOrder === 'desc' ? '$gt' : '$lt';
    }

    query[cursorField] = { [operator]: value };
    return query;
  }

  /**
   * Get sort object for MongoDB query
   */
  private getSortObject(
    config: CursorPaginationConfig,
    direction: 'next' | 'prev',
  ): Record<string, 1 | -1> {
    const { cursorField, sortOrder } = config;
    let sortValue: 1 | -1;

    if (direction === 'next') {
      sortValue = sortOrder === 'desc' ? -1 : 1;
    } else {
      // For prev direction, reverse the sort order
      sortValue = sortOrder === 'desc' ? 1 : -1;
    }

    return { [cursorField]: sortValue };
  }

  /**
   * Check if there are more items in the given direction
   */
  private async hasMoreItems<T extends Document>(
    model: Model<T>,
    baseQuery: FilterQuery<T>,
    lastItem: T | null,
    direction: 'next' | 'prev',
    config: CursorPaginationConfig,
  ): Promise<boolean> {
    if (!lastItem) {
      return false;
    }

    const cursorValue = lastItem[config.cursorField];
    const checkQuery: any = { ...baseQuery };

    let operator: string;
    if (direction === 'next') {
      operator = config.sortOrder === 'desc' ? '$lt' : '$gt';
    } else {
      operator = config.sortOrder === 'desc' ? '$gt' : '$lt';
    }

    checkQuery[config.cursorField] = { [operator]: cursorValue };

    const count = await model.countDocuments(checkQuery).exec();
    return count > 0;
  }

  /**
   * Perform cursor-based pagination
   */
  async paginate<T extends Document>(
    model: Model<T>,
    baseQuery: FilterQuery<T>,
    options: CursorPaginationOptionsDto,
    config: CursorPaginationConfig,
  ): Promise<CursorPaginationResultDto<T>> {
    const limit = options.limit || 10;
    const direction = options.direction || 'next';
    const cursor = options.cursor;

    // Build query with cursor conditions
    const query = this.buildCursorQuery(baseQuery, cursor, direction, config);

    // Get sort object
    const sort = this.getSortObject(config, direction);

    // Build the MongoDB query
    let mongoQuery = model
      .find(query)
      .sort(sort)
      .limit(limit + 1); // +1 to check if there are more items

    // Add population if specified
    if (config.populateFields && config.populateFields.length > 0) {
      config.populateFields.forEach(field => {
        mongoQuery = mongoQuery.populate(field);
      });
    }

    // Execute query
    const results = await mongoQuery.exec();

    // Check if we have more items than requested
    const hasMore = results.length > limit;
    const data = hasMore ? results.slice(0, limit) : results;

    // For prev direction, reverse the results to maintain correct order
    if (direction === 'prev') {
      data.reverse();
    }

    // Generate cursors
    const firstItem = data[0];
    const lastItem = data[data.length - 1];

    let nextCursor: string | null = null;
    let prevCursor: string | null = null;

    if (lastItem) {
      nextCursor = this.encodeCursor(
        lastItem[config.cursorField],
        config.cursorField,
      );
    }

    if (firstItem) {
      prevCursor = this.encodeCursor(
        firstItem[config.cursorField],
        config.cursorField,
      );
    }

    // Check for more items in both directions
    const hasNext =
      direction === 'next'
        ? hasMore
        : await this.hasMoreItems(model, baseQuery, lastItem, 'next', config);
    const hasPrevious =
      direction === 'prev'
        ? hasMore
        : await this.hasMoreItems(model, baseQuery, firstItem, 'prev', config);

    // If this is the first page (no cursor provided), there's no previous page
    if (!cursor && direction === 'next') {
      prevCursor = null;
    }

    const meta: CursorPaginationMetaDto = {
      nextCursor: hasNext ? nextCursor : null,
      prevCursor: hasPrevious ? prevCursor : null,
      hasNext,
      hasPrevious,
      count: data.length,
      limit,
    };

    return new CursorPaginationResultDto(data, meta);
  }
}

import {
  IsArray,
  IsEmail,
  IsEnum,
  IsNot<PERSON>mpty,
  IsO<PERSON>al,
  IsString,
  MinLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SystemUserRole } from '@common/schemas';

export class CreateUserDto {
  @ApiProperty({
    description: 'Username of the user',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(4)
  username: string;

  @ApiPropertyOptional({
    description: 'Email of the user',
    type: 'string',
  })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Password of the user',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({ enum: SystemUserRole })
  @IsEnum(SystemUserRole)
  role: SystemUserRole;
}

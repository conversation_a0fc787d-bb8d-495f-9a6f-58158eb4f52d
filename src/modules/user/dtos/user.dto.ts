import { Exclude, Expose, Type } from 'class-transformer';
import { SystemUserRole } from '@common/schemas';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SubscriptionDto } from '@module/subscription/dtos/subscription.dto';

export class UserDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  username: string;

  @ApiProperty()
  @Expose()
  email: string;

  @Exclude()
  password: string;

  @ApiProperty({ enum: SystemUserRole })
  @Expose()
  role: SystemUserRole;

  @ApiPropertyOptional()
  @Expose()
  subscriptionId?: string;

  @ApiPropertyOptional({ type: () => SubscriptionDto })
  @Expose()
  @Type(() => SubscriptionDto)
  subscription?: SubscriptionDto;

  constructor(partial: Partial<UserDto>) {
    Object.assign(this, partial);
  }
}

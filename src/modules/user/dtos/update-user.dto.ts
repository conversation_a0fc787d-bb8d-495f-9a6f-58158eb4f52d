import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  Is<PERSON><PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SystemUserRole } from '@common/schemas';

export class UpdateUserDto {
  @ApiProperty({
    description: 'Username of the user',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(4)
  username: string;

  @ApiPropertyOptional({
    description: 'Email of the user',
    type: 'string',
  })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Password of the user',
    type: 'string',
  })
  @IsOptional()
  @IsString()
  @MinLength(8)
  password?: string;

  @ApiProperty({ enum: SystemUserRole })
  @IsEnum(SystemUserRole)
  role: SystemUserRole;
}

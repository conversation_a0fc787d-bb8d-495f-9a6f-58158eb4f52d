import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SchemaCollectionName } from '@common/constants/schema';
import { SharedModule } from '@shared/shared.module';
import { AdminUserController } from '@module/user/controllers/admin-user.controller';
import { UserService } from '@module/user/user.service';
import {
  Subscription,
  SubscriptionSchema,
  SystemUser,
  SystemUserSchema,
} from '@common/schemas';
import { AdminGuard } from '@common/guards/admin.guard';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    SharedModule,
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
      {
        name: Subscription.name,
        schema: SubscriptionSchema,
        collection: SchemaCollectionName.Subscription,
      },
    ]),
  ],
  controllers: [AdminUserController],
  providers: [UserService, AdminGuard],
  exports: [UserService, AdminGuard],
})
export class UserModule {}

import { AppLogger } from '@common/logger/logger.service';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PaginationService } from '@shared/services/pagination.service';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import {
  Subscription,
  SubscriptionDocument,
  SystemUser,
  SystemUserDocument,
} from '@common/schemas';
import { UserPaginationResultDto } from '@module/user/dtos/user-pagination.dto';
import { UserDto } from '@module/user/dtos/user.dto';
import { CreatorDto } from '@module/user/dtos/creator.dto';
import { CreateUserDto } from '@module/user/dtos/create-user.dto';
import * as bcrypt from 'bcryptjs';
import { USER_ALREADY_EXIST, USER_NOT_EXIST } from '@common/exceptions/error';
import { UpdateUserDto } from '@module/user/dtos/update-user.dto';
import { SubscriptionDto } from '@module/subscription/dtos/subscription.dto';

@Injectable()
export class UserService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(SystemUser.name)
    private readonly userModel: Model<SystemUserDocument>,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
    private readonly paginationService: PaginationService,
  ) {
    this.logger.setContext(UserService.name);
  }

  async findOne(id: string): Promise<UserDto> {
    // Use populate with the correct foreign field
    const user = await this.userModel
      .findOne({ id })
      .populate('subscription')
      .exec();

    if (!user) {
      throw new NotFoundException(USER_NOT_EXIST);
    }

    return this.toDto(user);
  }

  async paginate(
    options: PaginationOptionsDto,
  ): Promise<UserPaginationResultDto> {
    // Create a custom paginate function that supports population
    const limit = options.limit > 0 ? options.limit : 10;
    const page = options.page > 0 ? options.page : 1;
    const sort = options.sort || '-createdAt';

    // Count total documents
    const total = await this.userModel.countDocuments({}).exec();

    // Create query with population
    const query = this.userModel
      .find({})
      .sort(sort)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate('subscription');

    // Execute query
    const data = await query.exec();

    // Convert to DTOs
    const userDtos = data.map(user => this.toDto(user));

    // Create pagination metadata
    const pages = Math.ceil(total / limit);
    const meta = { total, page, limit, pages };

    return {
      data: userDtos,
      meta,
    };
  }

  async create(input: CreateUserDto): Promise<UserDto> {
    const duplicateUser = await this.userModel.findOne({
      $or: [{ email: input.email }, { username: input.username }],
    });

    if (duplicateUser) {
      throw new ConflictException(USER_ALREADY_EXIST);
    }

    const hashedPassword = await bcrypt.hash(input.password, 10);

    const newUser = new this.userModel({
      email: input.email,
      username: input.username,
      password: hashedPassword,
      role: input.role,
    });

    await newUser.save();

    // Always fetch the user again to get any default values and populate subscription
    return this.findOne(newUser.id);
  }

  async update(id: string, input: UpdateUserDto): Promise<UserDto> {
    const user = await this.userModel.findOne({ id });

    if (!user) {
      throw new NotFoundException(USER_NOT_EXIST);
    }

    const duplicateUser = await this.userModel.findOne({
      $or: [{ email: input.email }, { username: input.username }],
    });

    if (duplicateUser && duplicateUser.id !== id) {
      throw new ConflictException(USER_ALREADY_EXIST);
    }

    user.email = input.email;
    user.username = input.username;
    user.role = input.role;

    if (input.password) {
      user.password = await bcrypt.hash(input.password, 10);
    }

    await user.save();

    // Always fetch the user again to get updated values and populate subscription
    return this.findOne(id);
  }

  async delete(id: string): Promise<UserDto> {
    // First get the user with subscription
    const userToReturn = await this.findOne(id);

    // Then delete the user
    const deletedUser = await this.userModel
      .findOneAndDelete({
        id,
      })
      .exec();

    if (!deletedUser) {
      throw new NotFoundException(USER_NOT_EXIST);
    }

    return userToReturn;
  }

  toDto(user: SystemUserDocument): UserDto {
    return new UserDto({
      id: user.id,
      username: user.username,
      email: user.email,
      password: user.password,
      role: user.role,
      subscriptionId: user.subscriptionId,
      subscription: user.subscription
        ? this.mapSubscriptionToDto(user.subscription)
        : null,
    });
  }

  private mapSubscriptionToDto(
    subscription: SubscriptionDocument,
  ): SubscriptionDto {
    return new SubscriptionDto({
      id: subscription.id,
      name: subscription.name,
      description: subscription.description,
      plan: subscription.plan,
      privileges: subscription.privileges,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt,
      // Note: We're not including createdBy to avoid circular references
      createdBy: undefined,
    });
  }

  /**
   * Creates a CreatorDto from a SystemUserDocument
   * This is used for representing a user as a creator in other entities
   */
  toCreatorDto(user: SystemUserDocument): CreatorDto {
    if (!user) return null;

    return new CreatorDto({
      id: user.id,
      username: user.username,
      email: user.email || '',
    });
  }

  /**
   * Fetches creators for multiple user IDs
   */
  async getCreatorsByIds(userIds: string[]): Promise<Map<string, CreatorDto>> {
    if (!userIds || userIds.length === 0) return new Map();

    const uniqueIds = [...new Set(userIds)];
    const users = await this.userModel.find({ id: { $in: uniqueIds } });

    return new Map(users.map(user => [user.id, this.toCreatorDto(user)]));
  }
}

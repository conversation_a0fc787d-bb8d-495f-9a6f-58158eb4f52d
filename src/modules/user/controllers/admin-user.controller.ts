import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { UserPaginationResultDto } from '@module/user/dtos/user-pagination.dto';
import { UserService } from '@module/user/user.service';
import { AdminGuard } from '@common/guards/admin.guard';
import { CreateUserDto } from '@module/user/dtos/create-user.dto';
import { UserDto } from '@module/user/dtos/user.dto';
import { UpdateUserDto } from '@module/user/dtos/update-user.dto';
import { CurrentUser } from '@common/decorators';
import { UserPayload } from '@module/user/types';

@UseInterceptors(ClassSerializerInterceptor)
@ApiTags('User Management for Admin')
@Controller('/admin/users')
export class AdminUserController {
  constructor(private readonly userService: UserService) {}

  @UseGuards(AdminGuard)
  @Get('/')
  @ApiOkResponse({ type: UserPaginationResultDto })
  paginate(
    @Query() paginationOptions: PaginationOptionsDto,
  ): Promise<UserPaginationResultDto> {
    return this.userService.paginate(paginationOptions);
  }

  @UseGuards(AdminGuard)
  @ApiOkResponse({ type: UserDto })
  @Get('/:id')
  findOne(@Param('id') id: string) {
    return this.userService.findOne(id);
  }

  @UseGuards(AdminGuard)
  @ApiOkResponse({ type: UserDto })
  @Post('/')
  create(@Body() input: CreateUserDto) {
    return this.userService.create(input);
  }

  @UseGuards(AdminGuard)
  @ApiOkResponse({ type: UserDto })
  @Put('/:id')
  update(@Param('id') id: string, @Body() input: UpdateUserDto) {
    return this.userService.update(id, input);
  }

  @UseGuards(AdminGuard)
  @ApiOkResponse({ type: UserDto })
  @Delete('/:id')
  delete(@Param('id') id: string, @CurrentUser() user: UserPayload) {
    if (user.id === id) {
      throw new ForbiddenException('You cannot delete yourself');
    }
    return this.userService.delete(id);
  }
}

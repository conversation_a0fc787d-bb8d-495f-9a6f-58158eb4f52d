import { Module } from '@nestjs/common';
import { LoggerModule } from '@common/logger/logger.module';
import { TestController } from './test.controller';
import { TestService } from './test.service';
import { AdminGuard } from '@common/guards/admin.guard';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import {
  LiveSession,
  LiveSessionSchema,
  PlatformUser,
  PlatformUserSchema,
  Subscription,
  SubscriptionSchema,
  SystemUser,
  SystemUserSchema,
} from '@common/schemas';
import { SchemaCollectionName } from '@common/constants/schema';
import {
  PlatformChannel,
  PlatformChannelSchema,
} from '@common/schemas/platform-channel.schema';
import { Comment, CommentSchema } from '@common/schemas/comment.schema';
import { PlatformUserModule } from '@module/platform-user/platform-user.module';
import { CommentModule } from '@module/comment/comment.module';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    PlatformUserModule,
    CommentModule,
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
      {
        name: PlatformChannel.name,
        schema: PlatformChannelSchema,
        collection: SchemaCollectionName.PlatformChannel,
      },
      {
        name: Subscription.name,
        schema: SubscriptionSchema,
        collection: SchemaCollectionName.Subscription,
      },
      {
        name: PlatformUser.name,
        schema: PlatformUserSchema,
        collection: SchemaCollectionName.PlatformUser,
      },
      {
        name: Comment.name,
        schema: CommentSchema,
        collection: SchemaCollectionName.Comment,
      },
      {
        name: LiveSession.name,
        schema: LiveSessionSchema,
        collection: SchemaCollectionName.LiveSession,
      },
    ]),
  ],
  controllers: [TestController],
  providers: [TestService, AdminGuard],
  exports: [TestService],
})
export class TestModule {}

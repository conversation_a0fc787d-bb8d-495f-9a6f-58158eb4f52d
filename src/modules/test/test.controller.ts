import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { TestService } from './test.service';
import { MigrationResponseDto } from '@common/dtos/migration-response.dto';
import { AdminGuard } from '@common/guards/admin.guard';

@ApiTags('Test')
@Controller('/test')
export class TestController {
  constructor(private readonly testService: TestService) {}

  @Get('/status')
  @ApiOkResponse({ description: 'Returns OK status if the service is running' })
  getStatus() {
    return this.testService.getStatus();
  }

  @UseGuards(AdminGuard)
  @Post('/migrate')
  @ApiOkResponse({ description: 'Returns OK status if the service is running' })
  migrate() {
    return this.testService.migrate();
  }

  @UseGuards(AdminGuard)
  @Post('/migrate-subscription')
  @ApiOperation({
    summary: 'Migrate users without subscription to free subscription plan',
  })
  @ApiOkResponse({
    description: 'Migration results',
    type: MigrationResponseDto,
  })
  migrateSubscriptions(): Promise<MigrationResponseDto> {
    return this.testService.migrateSubscriptions();
  }

  @UseGuards(AdminGuard)
  @Post('/migrate-system-users')
  @ApiOperation({
    summary: 'Migrate all SystemUser records to update timestamp fields',
    description:
      'Updates createdAt, updatedAt, and activeAt fields for all existing SystemUser records to current timestamp',
  })
  @ApiOkResponse({
    description: 'Migration results',
    type: MigrationResponseDto,
  })
  migrateSystemUsers(): Promise<MigrationResponseDto> {
    return this.testService.migrateSystemUsers();
  }

  @UseGuards(AdminGuard)
  @Post('/migrate-platform-users')
  @ApiOperation({
    summary: 'Migrate all PlatformUser records to update id (uuid) fields',
  })
  @ApiOkResponse({
    description: 'Migration results',
    type: MigrationResponseDto,
  })
  migratePlatformUsers(): Promise<MigrationResponseDto> {
    return this.testService.migratePlatformUsers();
  }

  @UseGuards(AdminGuard)
  @Post('/migrate-live-sessions')
  @ApiOperation({
    summary: 'Migrate all Comments to table',
  })
  @ApiOkResponse({
    description: 'Migration results',
    type: MigrationResponseDto,
  })
  migrateLiveSessions(): Promise<MigrationResponseDto> {
    return this.testService.migrateLiveSessions();
  }

  @UseGuards(AdminGuard)
  @Post('/migrate-comments')
  @ApiOperation({
    summary: 'Migrate all Comments to table',
  })
  @ApiOkResponse({
    description: 'Migration results',
    type: MigrationResponseDto,
  })
  migrateComments(): Promise<MigrationResponseDto> {
    return this.testService.migrateComments();
  }
}

import { AppLogger } from '@common/logger/logger.service';
import { LiveManagerService } from '@module/live-manager/live-manager.service';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';
import { WebcastPushConnection } from '@vulinhscoris/tiktok-live-connector';
import { LivestreamPlatform } from '@common/constants';
import { ensureTrailingSlash } from '@common/utils/link';
import { HEADLESS_SERVER } from '@common/config/enviroment.config';
import { SystemConfigService } from '@module/system-config/system-config.service';
import { BaseLiveCommentService } from '@module/live-comment/services/base-live-comment.service';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { TikTokCommentMapper } from '@module/live-comment/mappers/tiktok-comment.mapper';
import { TikTokClientComment } from '@module/live-comment/types/tiktok';
import { LiveSessionRedisService } from '../redis/live-session.redis';

interface ITiktokSession {
  username: string;
  connection: WebcastPushConnection;
}

@Injectable()
export class TiktokLiveCommentService extends BaseLiveCommentService {
  private tiktokLiveConnections: Map<string, ITiktokSession[]> = new Map();
  private commentMapper: TikTokCommentMapper = new TikTokCommentMapper();

  constructor(
    protected readonly logger: AppLogger,
    private readonly liveManagerService: LiveManagerService,
    private readonly userService: PlatformUserService,
    protected readonly liveSessionService: LiveSessionService,
    private readonly systemConfigService: SystemConfigService,
    protected readonly liveSessionRedisService: LiveSessionRedisService,
  ) {
    super(liveSessionService, liveSessionRedisService, logger);
    this.logger.setContext(TiktokLiveCommentService.name);
  }

  async createLive(
    socketClient: Socket,
    username: string,
    userId: string,
    saveHistory: boolean,
    liveId: string,
    platformChannelId: string,
    sessionId: string,
  ): Promise<void> {
    // this.platformChannelId = platformChannelId;
    // this.setSessionId(sessionId);
    //
    // try {
    //   if (!userId) {
    //     userId = socketClient.id;
    //   }
    //
    //   const existSession = await this.__checkExistConnection(userId, username);
    //   if (existSession) {
    //     return;
    //   }
    //   const { tiktokLiveConnection, liveSessionId } =
    //     await this.__createNewConnection(
    //       socketClient,
    //       userId,
    //       username,
    //       saveHistory,
    //     );
    //
    //   tiktokLiveConnection.on('chat', (data: any) => {
    //     if (saveHistory) {
    //       this.liveManagerService.saveCommentToLiveSession(
    //         LivestreamPlatform.TIKTOK,
    //         liveSessionId,
    //         userId,
    //         this.commentMapper.fromPlatformToEntity(data),
    //       );
    //     }
    //
    //     this.__sendMessageToClient(socketClient, username, {
    //       msgId: data.msgId,
    //       profilePictureUrl: data.profilePictureUrl,
    //       nickName: data.nickname,
    //       username: data.uniqueId,
    //       message: data.comment,
    //       userId: data.userId,
    //     });
    //   });
    //
    //   tiktokLiveConnection.on('streamEnd', async (actionId: string) => {
    //     this.logger.log(`[DEBUG_HERE] streamEnd by action id: ${actionId}`);
    //     this.__closeConnection(userId, username);
    //     await this.liveSessionRedisService.deleteSession(this.getSessionId());
    //   });
    //
    //   tiktokLiveConnection.on('error', async error => {
    //     this.logger.error(
    //       `[DEBUG_HERE] Error on TikTok: ${error.message} | ${error.code}`,
    //       JSON.stringify(error),
    //     );
    //     await this.liveSessionRedisService.deleteSession(this.getSessionId());
    //   });
    // } catch (error) {
    //   this.logger.error(
    //     `[DEBUG_HERE] Error create tiktok live ${error.message} | ${error.code}`,
    //     JSON.stringify(error),
    //   );
    // }
  }

  async __closeConnection(userId: string, username: string): Promise<void> {
    const tiktokSessions = this.tiktokLiveConnections.get(userId);

    if (!tiktokSessions || tiktokSessions.length === 0) {
      this.logger.warn(`No active sessions found for userId: ${userId}`);
      return;
    }

    const remainingSessions = tiktokSessions.filter(session => {
      if (session.username == username) {
        session.connection.disconnect();
        this.logger.log(
          `Disconnected from TikTok Live for device: ${userId} | username: ${username}`,
        );
        return false;
      }
      return true;
    });

    if (remainingSessions.length > 0) {
      this.tiktokLiveConnections.set(userId, remainingSessions);
    } else {
      this.tiktokLiveConnections.delete(userId);
      this.logger.log(
        `No remaining sessions for userId: ${userId}, removing from map.`,
      );
    }
  }

  async __sendMessageToClient(
    socketClient: Socket,
    username: string,
    data: Omit<
      TikTokClientComment,
      'phoneNumber' | 'isOldCustomer' | 'hasCanceledOrder'
    >,
  ): Promise<void> {
    const newData: TikTokClientComment = {
      ...data,
      phoneNumber: '',
      isOldCustomer: false,
      hasCanceledOrder: false,
    };
    const existUser = await this.userService.getUserById(data.userId);
    if (existUser) {
      newData.phoneNumber = existUser.phoneNumber;
      newData.isOldCustomer = true;
    }

    newData.hasCanceledOrder = existUser?.hasCanceledOrder || false;

    this.emitToClient(
      socketClient,
      newData.msgId,
      this.commentMapper.getClientEventName(),
      username,
      newData,
      LivestreamPlatform.TIKTOK,
    );
  }

  async __checkExistConnection(
    userId: string,
    username: string,
  ): Promise<boolean> {
    const existSession = this.tiktokLiveConnections.get(userId);
    if (existSession) {
      const existUsernameSession = existSession.find(
        item => item.username == username,
      );

      if (existUsernameSession) {
        return true;
      }
    }

    return false;
  }

  async __createNewConnection(
    socketClient: Socket,
    userId: string,
    username: string,
    saveHistory = false,
  ): Promise<{
    tiktokLiveConnection: any;
    liveSessionId: string;
  }> {
    const tiktokLiveConnection = await this.__getConnection(username);

    const existSession = this.tiktokLiveConnections.get(userId);

    if (existSession) {
      existSession.push({
        username: username,
        connection: tiktokLiveConnection,
      });
      this.tiktokLiveConnections.set(userId, existSession);
    } else {
      const newSession = [
        {
          username: username,
          connection: tiktokLiveConnection,
        },
      ] as ITiktokSession[];
      this.tiktokLiveConnections.set(userId, newSession);
    }

    if (this.platformChannelId) {
      try {
        // Map disconnect callback for this connection
        await this.mapDisconnectCallback(
          socketClient.id,
          this.platformChannelId,
          () => {
            tiktokLiveConnection.disconnect();
          },
        );
      } catch (error) {
        this.logger.error(
          `Error mapping disconnect callback: ${error.message}`,
          error.stack,
        );
      }
    }

    const liveState = tiktokLiveConnection.getState() as any;
    let liveSessionId = '';
    if (saveHistory) {
      // const oldComments =
      //   await this.liveSessionService.getCommentsByLiveSessionId(
      //     liveState?.roomId,
      //     // userId,
      //   );
      //
      // if (oldComments?.length > 0) {
      //   await this.sendToClientByComments(
      //     socketClient,
      //     oldComments,
      //     username,
      //     LivestreamPlatform.TIKTOK,
      //   );
      // }

      liveSessionId = await this.liveManagerService.createLiveSession(
        LivestreamPlatform.TIKTOK,
        userId,
        username,
        liveState?.roomId,
        liveState?.roomInfo?.title || '',
        liveState?.roomInfo?.share_url,
      );
    }
    return { tiktokLiveConnection, liveSessionId };
  }

  async __createDefaultConnection(username: string): Promise<any> {
    const tiktokLiveConnection = new WebcastPushConnection(username);

    const state = await tiktokLiveConnection.connect();

    this.logger.log(
      `Connected to roomId ${state.roomId} with userId ${username}`,
    );

    return tiktokLiveConnection;
  }

  async __createBackupConnection(username: string): Promise<any> {
    const tiktokLiveConnection = new WebcastPushConnection(username, {
      signProviderOptions: {
        timeout: 20000,
        host: ensureTrailingSlash(HEADLESS_SERVER),
        params: {
          username,
          apiKey: process.env.X_API_KEY,
        },
        only: true,
      },
    });

    const state = await tiktokLiveConnection.connect();

    this.logger.log(
      `[Backup] Connected to roomId ${state.roomId} with userId ${username}`,
    );

    return tiktokLiveConnection;
  }

  async __getConnection(username: string): Promise<any> {
    const shouldPrioritizeBackupTiktok =
      await this.systemConfigService.shouldPrioritizeBackupTiktok();

    this.logger.log(`Tiktok backup priority: ${shouldPrioritizeBackupTiktok}`);

    const connections = [
      () => this.__createDefaultConnection(username),
      () => this.__createBackupConnection(username),
    ];

    if (shouldPrioritizeBackupTiktok) {
      connections.reverse();
    }

    for (const connection of connections) {
      try {
        return await connection();
      } catch (error) {
        this.logger.error(
          `[DEBUG_HERE] Error creating tiktok connection: ${error.message} | ${
            error.code
          } : ${JSON.stringify(error)}`,
        );
      }
    }
  }
}

import { AppLogger } from '@common/logger/logger.service';
import { LiveManagerService } from '@module/live-manager/live-manager.service';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';
import { Socket as SocketClient, io } from 'socket.io-client';
import { AppException } from '@common/exceptions/app-exception';
import { LIVE_CHANNEL_NOT_EXIST } from '@common/exceptions/error';
import { LivestreamPlatform } from '@common/constants';
import { HEADLESS_SERVER } from '@common/config/enviroment.config';
import { BaseLiveCommentService } from '@module/live-comment/services/base-live-comment.service';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { FacebookCommentMapper } from '@module/live-comment/mappers/facebook-comment.mapper';
import { LiveSessionRedisService } from '../redis/live-session.redis';

interface IFacebookSession {
  liveId: string;
  connection: SocketClient;
}

@Injectable()
export class FacebookLiveCommentService extends BaseLiveCommentService {
  private facebookLiveConnections: Map<string, IFacebookSession[]> = new Map();
  private commentMapper: FacebookCommentMapper = new FacebookCommentMapper();

  constructor(
    protected readonly logger: AppLogger,
    private readonly liveManagerService: LiveManagerService,
    private readonly userService: PlatformUserService,
    protected readonly liveSessionService: LiveSessionService,
    protected readonly liveSessionRedisService: LiveSessionRedisService,
  ) {
    super(liveSessionService, liveSessionRedisService, logger);
    this.logger.setContext(FacebookLiveCommentService.name);
  }

  async createLive(
    socketClient: Socket,
    username: string,
    userId: string,
    saveHistory: boolean,
    liveId: string,
    platformChannelId: string,
    sessionId: string,
  ): Promise<void> {
    // try {
    //   this.platformChannelId = platformChannelId;
    //   this.setSessionId(sessionId);
    //   if (!liveId) {
    //     throw new AppException(LIVE_CHANNEL_NOT_EXIST);
    //   }
    //
    //   if (!userId) {
    //     userId = socketClient.id;
    //   }
    //
    //   const existSession = await this.__checkExistConnection(userId, liveId);
    //   if (existSession) {
    //     return;
    //   }
    //
    //   // const oldComments =
    //   //   await this.liveSessionService.getCommentsByLiveSessionId(
    //   //     liveId,
    //   //     // userId,
    //   //   );
    //   //
    //   // if (oldComments.length > 0) {
    //   //   await this.sendToClientByComments(
    //   //     socketClient,
    //   //     oldComments,
    //   //     liveId,
    //   //     LivestreamPlatform.FACEBOOK,
    //   //   );
    //   // }
    //
    //   const { facebookLiveConnection, liveSessionId } =
    //     await this.__createNewConnection(
    //       socketClient,
    //       userId,
    //       liveId,
    //       saveHistory,
    //     );
    //
    //   facebookLiveConnection.on('chat', data => {
    //     try {
    //       if (saveHistory) {
    //         this.liveManagerService.saveCommentToLiveSession(
    //           LivestreamPlatform.FACEBOOK,
    //           liveSessionId,
    //           userId,
    //           this.commentMapper.fromPlatformToEntity(data),
    //         );
    //       }
    //
    //       this.__sendMessageToClient(socketClient, liveId, data);
    //     } catch (error) {
    //       this.logger.error(`Error processing chat data: ${error.message}`);
    //     }
    //   });
    //
    //   facebookLiveConnection.on('disconnect', async () => {
    //     try {
    //       this.logger.log(`Stream end | liveId=${liveId}`);
    //       facebookLiveConnection.removeAllListeners();
    //       this.__closeConnection(userId, liveId);
    //       await this.liveSessionRedisService.deleteSession(this.getSessionId());
    //     } catch (error) {
    //       this.logger.error(`Error on stream end: ${error.message}`);
    //     }
    //   });
    //
    //   facebookLiveConnection.on('connect_error', async error => {
    //     this.logger.error(`Error on connect: ${error.message}`);
    //     this.__closeConnection(userId, liveId);
    //     await this.liveSessionRedisService.deleteSession(this.getSessionId());
    //   });
    //
    //   socketClient.on('disconnect', async () => {
    //     try {
    //       this.logger.log(`Socket disconnected | liveId=${liveId}`);
    //       facebookLiveConnection.disconnect();
    //       this.__closeConnection(userId, liveId);
    //     } catch (error) {
    //       this.logger.error(`Error on socket disconnect: ${error.message}`);
    //     } finally {
    //       await this.liveSessionRedisService.deleteSession(this.getSessionId());
    //     }
    //   });
    // } catch (error) {
    //   this.logger.error(`Error creating Facebook live: ${error.message}`);
    // }
  }

  async __sendMessageToClient(
    socketClient: Socket,
    liveId: string,
    data: any,
  ): Promise<void> {
    const response = {
      commentId: data?.commentid || '',
      userId: data?.profileId || '',
      name: data?.chatname || '',
      picture: data?.chatimg || '',
      message: data?.chatmessage || '',
      createTime: data.createdAt,
      isOldCustomer: false,
      phoneNumber: '',
      hasCanceledOrder: false,
    };

    if (data?.profileId) {
      const existUser = await this.userService.getUserById(data.profileId);
      if (existUser) {
        response.phoneNumber = existUser.phoneNumber || '';
        response.isOldCustomer = true;
      }

      response.hasCanceledOrder = existUser?.hasCanceledOrder || false;
    }

    this.emitToClient(
      socketClient,
      data.commentid,
      this.commentMapper.getClientEventName(),
      liveId,
      response,
      LivestreamPlatform.FACEBOOK,
    );
  }

  async __closeConnection(userId: string, liveId: string): Promise<void> {
    const facebookSessions = this.facebookLiveConnections.get(userId);

    if (!facebookSessions || facebookSessions.length === 0) {
      this.logger.warn(`No active sessions found for userId: ${userId}`);
      return;
    }

    const remainingSessions = facebookSessions.filter(session => {
      if (session.liveId === liveId) {
        this.logger.log(
          `Disconnected from Facebook Live for userId: ${userId}, liveId: ${liveId}`,
        );
        return false;
      }
      return true;
    });

    if (remainingSessions.length > 0) {
      this.facebookLiveConnections.set(userId, remainingSessions);
    } else {
      this.facebookLiveConnections.delete(userId);
      this.logger.log(
        `No remaining sessions for userId: ${userId}, removing from map.`,
      );
    }
  }

  async __checkExistConnection(
    userId: string,
    liveId: string,
  ): Promise<boolean> {
    const existSession = this.facebookLiveConnections.get(userId) || [];
    return existSession.some(session => session.liveId === liveId);
  }

  async __createNewConnection(
    socketClient: Socket,
    userId: string,
    liveId: string,
    saveHistory = false,
  ): Promise<{
    facebookLiveConnection: SocketClient;
    liveSessionId: string;
  }> {
    try {
      const facebookLiveConnection = io(HEADLESS_SERVER);

      facebookLiveConnection.emit('live', {
        platform: LivestreamPlatform.FACEBOOK,
        id: liveId,
        userId,
      });

      if (this.platformChannelId) {
        await this.mapDisconnectCallback(
          socketClient.id,
          this.platformChannelId,
          () => {
            facebookLiveConnection.disconnect();
          },
        );
      }

      const sessions = this.facebookLiveConnections.get(userId) || [];

      sessions.push({
        liveId,
        connection: facebookLiveConnection,
      });

      this.facebookLiveConnections.set(userId, sessions);

      let liveSessionId = '';

      if (saveHistory) {
        liveSessionId = await this.liveManagerService.createLiveSession(
          LivestreamPlatform.FACEBOOK,
          userId,
          liveId,
          liveId,
          `https://www.facebook.com/${liveId}`,
          `https://www.facebook.com/${liveId}`,
        );
      }

      return { facebookLiveConnection, liveSessionId };
    } catch (error) {
      this.logger.error(
        `Failed to create connection for liveId: ${liveId}. Error: ${error.message}`,
      );
      throw new AppException(LIVE_CHANNEL_NOT_EXIST);
    }
  }
}

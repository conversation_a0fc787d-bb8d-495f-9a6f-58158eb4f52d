import { Socket } from 'socket.io';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { LivestreamPlatform } from '@common/constants';
import { LiveSessionRedisService } from '../redis/live-session.redis';
import { TempComment } from '@common/schemas';
import { AppLogger } from '@common/logger/logger.service';

export abstract class BaseLiveCommentService {
  protected platformChannelId: string;
  protected sessionId: string;

  constructor(
    protected readonly liveSessionService: LiveSessionService,
    protected readonly liveSessionRedisService: LiveSessionRedisService,
    protected readonly logger: AppLogger,
  ) {
    this.logger.setContext(this.constructor.name);
  }

  abstract createLive(
    socketClient: Socket,
    username: string,
    userId: string,
    saveHistory: boolean,
    liveId: string,
    platformChannelId: string,
    sessionId: string,
  ): Promise<void>;

  setSessionId(sessionId: string) {
    this.sessionId = sessionId;
  }

  getSessionId() {
    return this.sessionId;
  }

  async sendToClientByComments(
    socket: Socket,
    comments: TempComment[],
    liveId: string,
    platform: LivestreamPlatform,
  ): Promise<void> {
    // if (!comments || comments.length === 0) {
    //   return;
    // }
    //
    // console.log(
    //   `Send old comments to client: ${comments.length} comments, liveId: ${liveId}, platform: ${platform}`,
    // );
    //
    // const factory = new CommentMapperFactory();
    //
    // const commentMapper = factory.init(platform);
    //
    // for (const comment of comments) {
    //   const data = commentMapper.fromEntityToClient(comment);
    //   await this.emitToClient(
    //     socket,
    //     comment.msgId,
    //     commentMapper.getClientEventName(),
    //     liveId,
    //     data,
    //     platform,
    //   );
    //
    //   await sleep(50);
    // }
  }

  /**
   * Map a disconnect callback for a socket client and platform channel
   */
  protected async mapDisconnectCallback(
    socketClientId: string,
    platformChannelId: string,
    disconnectCallback: () => void,
  ): Promise<void> {
    try {
      // Get the sessionId from the socket client
      const sessionIds =
        await this.liveSessionRedisService.getSessionsBySocketId(
          socketClientId,
        );

      // Find the session with matching platformChannelId
      for (const sessionId of sessionIds) {
        const sessionData = await this.liveSessionRedisService.get(sessionId);
        if (
          sessionData &&
          sessionData.platformChannelId === platformChannelId
        ) {
          // Map disconnect callback using sessionId
          this.liveSessionRedisService.mapDisconnect(
            sessionId,
            platformChannelId,
            disconnectCallback,
          );
          break;
        }
      }
    } catch (error) {
      this.logger.error(
        `Error mapping disconnect callback: ${error.message}`,
        error.stack,
      );
    }
  }

  async emitToClient(
    socket: Socket,
    commentId: string,
    eventName: string,
    liveId: string,
    data: any,
    platform: LivestreamPlatform,
  ): Promise<void> {
    try {
      // Check if this comment has already been sent to this session
      if (
        await this.liveSessionRedisService.hasSentComment(
          this.sessionId,
          commentId,
        )
      ) {
        return;
      }
    } catch (error) {
      this.logger.error(
        `Error checking sent comment: ${error.message}`,
        error.stack,
      );
    }

    socket.emit(eventName, data);

    if (platform === LivestreamPlatform.FACEBOOK) {
      socket.emit(`${eventName}-${socket.id}`, data);
    } else {
      socket.emit(`${eventName}-${socket.id}-${liveId}`, data);
      socket.emit(`${eventName}-${socket.id}`, data);
    }

    try {
      // Add the comment to the sent comments for this session
      await this.liveSessionRedisService.addSentComment(
        this.getSessionId(),
        commentId,
      );
    } catch (error) {
      this.logger.error(
        `Error adding sent comment: ${error.message}`,
        error.stack,
      );
    }
  }
}

import { Injectable } from '@nestjs/common';
import { AppLogger } from '@common/logger/logger.service';
import { LivestreamPlatform } from '@common/constants';
import { ILiveCommentService } from './live-comment.interface';
import { TiktokLiveCommentService } from './tiktok-comment.service';
import { YoutubeLiveCommentService } from './youtube-comment.service';
import { AppException } from '@common/exceptions/app-exception';
import { PLATFORM_NOT_SUPPORT } from '@common/exceptions/error';
import { LiveManagerService } from '@module/live-manager/live-manager.service';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { FacebookLiveCommentService } from '@module/live-comment/services/facebook-comment.service';
import { ConfigService } from '@nestjs/config';
import { SystemConfigService } from '@module/system-config/system-config.service';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { LiveSessionRedisService } from '../redis/live-session.redis';

@Injectable()
export class LiveCommentFactory {
  constructor(
    private logger: AppLogger,
    private readonly liveManagerService: LiveManagerService,
    private readonly userService: PlatformUserService,
    private readonly configService: ConfigService,
    private readonly systemConfigService: SystemConfigService,
    private readonly liveSessionService: LiveSessionService,
    private readonly liveSessionRedisService: LiveSessionRedisService,
  ) {}

  createLiveService(platform: LivestreamPlatform): ILiveCommentService {
    switch (platform) {
      case LivestreamPlatform.TIKTOK:
        return new TiktokLiveCommentService(
          this.logger,
          this.liveManagerService,
          this.userService,
          this.liveSessionService,
          this.systemConfigService,
          this.liveSessionRedisService,
        );
      case LivestreamPlatform.YOUTUBE:
        return new YoutubeLiveCommentService(
          this.logger,
          this.liveManagerService,
          this.userService,
          this.liveSessionService,
          this.configService,
          this.liveSessionRedisService,
        );
      case LivestreamPlatform.FACEBOOK:
        return new FacebookLiveCommentService(
          this.logger,
          this.liveManagerService,
          this.userService,
          this.liveSessionService,
          this.liveSessionRedisService,
        );
      default:
        throw new AppException(PLATFORM_NOT_SUPPORT);
    }
  }
}

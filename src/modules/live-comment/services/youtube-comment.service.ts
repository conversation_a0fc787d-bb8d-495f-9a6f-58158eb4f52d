import { AppLogger } from '@common/logger/logger.service';
import { LiveManagerService } from '@module/live-manager/live-manager.service';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';
import { AppException } from '@common/exceptions/app-exception';
import { LIVE_CHANNEL_NOT_EXIST } from '@common/exceptions/error';
import {
  AddChatItemAction,
  Masterchat,
  MasterchatError,
  MasterchatOptions,
} from 'masterchat';
import { YoutubeClientComment } from '../types/youtube';
import { LivestreamPlatform } from '@common/constants';
import { BaseLiveCommentService } from '@module/live-comment/services/base-live-comment.service';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { YoutubeCommentMapper } from '@module/live-comment/mappers/youtube-comment.mapper';
import { LiveSessionRedisService } from '../redis/live-session.redis';
import { ConfigService } from '@nestjs/config';

interface IYoutubeSession {
  liveId: string;
  connection: Masterchat;
}

@Injectable()
export class YoutubeLiveCommentService extends BaseLiveCommentService {
  private youtubeLiveConnections: Map<string, IYoutubeSession[]> = new Map();
  private commentMapper: YoutubeCommentMapper = new YoutubeCommentMapper();

  constructor(
    protected readonly logger: AppLogger,
    private readonly liveManagerService: LiveManagerService,
    private readonly userService: PlatformUserService,
    protected readonly liveSessionService: LiveSessionService,
    private readonly configService: ConfigService,
    protected readonly liveSessionRedisService: LiveSessionRedisService,
  ) {
    super(liveSessionService, liveSessionRedisService, logger);
    this.logger.setContext(YoutubeLiveCommentService.name);
  }

  async createLive(
    socketClient: Socket,
    username: string,
    userId: string,
    saveHistory: boolean,
    liveId: string,
    platformChannelId: string,
    sessionId: string,
  ): Promise<void> {
    // try {
    //   this.platformChannelId = platformChannelId;
    //   this.setSessionId(sessionId);
    //
    //   if (!liveId) {
    //     throw new AppException(LIVE_CHANNEL_NOT_EXIST);
    //   }
    //
    //   if (!userId) {
    //     userId = socketClient.id;
    //   }
    //
    //   const existSession = await this.__checkExistConnection(userId, liveId);
    //   if (existSession) {
    //     return;
    //   }
    //
    //   const { youtubeLiveConnection, liveSessionId } =
    //     await this.__createNewConnection(
    //       socketClient,
    //       userId,
    //       liveId,
    //       saveHistory,
    //     );
    //
    //   youtubeLiveConnection.on('chat', data => {
    //     try {
    //       const youtubeMessage = this.__convertChatModel(data);
    //
    //       if (saveHistory) {
    //         this.liveManagerService.saveCommentToLiveSession(
    //           LivestreamPlatform.YOUTUBE,
    //           liveSessionId,
    //           userId,
    //           this.commentMapper.fromPlatformToEntity(data),
    //         );
    //       }
    //
    //       if (youtubeMessage) {
    //         this.__sendMessageToClient(socketClient, liveId, youtubeMessage);
    //       }
    //     } catch (error) {
    //       this.logger.error(
    //         `[DEBUG_HERE] Error processing chat data: ${error.message} | ${error.code}`,
    //       );
    //     }
    //   });
    //
    //   youtubeLiveConnection.on('end', async () => {
    //     try {
    //       this.logger.log(`[DEBUG_HERE] Stream end | liveId=${liveId}`);
    //       youtubeLiveConnection.removeAllListeners();
    //       this.__closeConnection(userId, liveId);
    //       await this.liveSessionRedisService.deleteSession(this.getSessionId());
    //     } catch (error) {
    //       this.logger.error(
    //         `[DEBUG_HERE] Error on stream end: ${error.message} | ${error.code}`,
    //       );
    //     }
    //   });
    //
    //   youtubeLiveConnection.on('error', async (error: MasterchatError) => {
    //     this.logger.error(
    //       `[DEBUG_HERE] Error on Masterchat: ${error.message} | ${error.code}`,
    //     );
    //     this.__closeConnection(userId, liveId);
    //     await this.liveSessionRedisService.deleteSession(this.getSessionId());
    //   });
    //
    //   youtubeLiveConnection.listen();
    // } catch (error) {
    //   this.logger.error(
    //     `[DEBUG_HERE] Error creating YouTube live: ${error.message} | ${error.code}`,
    //   );
    // }
  }

  async __sendMessageToClient(
    socketClient: Socket,
    liveId: string,
    data: YoutubeClientComment,
  ): Promise<void> {
    const response = {
      ...data,
      isOldCustomer: false,
      phoneNumber: '',
      hasCanceledOrder: false,
    };

    const existUser = await this.userService.getUserById(data.author.channelId);
    if (existUser) {
      response.phoneNumber = existUser.phoneNumber || '';
      response.isOldCustomer = true;
    }

    response.hasCanceledOrder = existUser?.hasCanceledOrder || false;

    this.emitToClient(
      socketClient,
      data.id,
      this.commentMapper.getClientEventName(),
      liveId,
      response,
      LivestreamPlatform.YOUTUBE,
    );
  }

  __convertChatModel(data: AddChatItemAction): YoutubeClientComment {
    if (!data || !data.authorName || !data.message) {
      this.logger.warn(`Invalid chat data: ${JSON.stringify(data)}`);
      return null;
    }

    return {
      id: data.id,
      author: {
        name: data.authorName,
        thumbnail: {
          url: data.authorPhoto,
          alt: '',
        },
        channelId: data.authorChannelId,
        badge: {
          thumbnail: {
            url: data.authorPhoto,
            alt: '',
          },
          label: '',
        },
      },
      message: this.commentMapper.parseMessages(data.message),
      isMembership: Boolean(data.membership),
      isVerified: data.isVerified,
      isOwner: data.isOwner,
      isModerator: data.isModerator,
      timestamp: data.timestamp,
      isOldCustomer: false,
      hasCanceledOrder: false,
      phoneNumber: '',
    };
  }

  async __closeConnection(userId: string, liveId: string): Promise<void> {
    const youtubeSessions = this.youtubeLiveConnections.get(userId);

    if (!youtubeSessions || youtubeSessions.length === 0) {
      this.logger.warn(`No active sessions found for userId: ${userId}`);
      return;
    }

    const remainingSessions = youtubeSessions.filter(session => {
      if (session.liveId === liveId) {
        this.logger.log(
          `Disconnected from YouTube Live for userId: ${userId}, liveId: ${liveId}`,
        );
        return false;
      }
      return true;
    });

    if (remainingSessions.length > 0) {
      this.youtubeLiveConnections.set(userId, remainingSessions);
    } else {
      this.youtubeLiveConnections.delete(userId);
      this.logger.log(
        `No remaining sessions for userId: ${userId}, removing from map.`,
      );
    }
  }

  async __checkExistConnection(
    userId: string,
    liveId: string,
  ): Promise<boolean> {
    const existSession = this.youtubeLiveConnections.get(userId) || [];
    return existSession.some(session => session.liveId === liveId);
  }

  async __createNewConnection(
    socketClient: Socket,
    userId: string,
    liveId: string,
    saveHistory = false,
  ): Promise<{
    youtubeLiveConnection: Masterchat;
    liveSessionId: string;
  }> {
    try {
      const options: MasterchatOptions = {};

      if (this.configService.get('YOUTUBE_CREDENTIALS')) {
        options.credentials = this.configService.get('YOUTUBE_CREDENTIALS');
      }

      const youtubeLiveConnection = await Masterchat.init(liveId, options);

      if (this.platformChannelId) {
        await this.mapDisconnectCallback(
          socketClient.id,
          this.platformChannelId,
          () => youtubeLiveConnection.stop(),
        );
      }

      const sessions = this.youtubeLiveConnections.get(userId) || [];

      sessions.push({
        liveId,
        connection: youtubeLiveConnection,
      });

      this.youtubeLiveConnections.set(userId, sessions);

      let liveSessionId = '';

      if (saveHistory) {
        // const oldComments =
        //   await this.liveSessionService.getCommentsByLiveSessionId(
        //     youtubeLiveConnection.videoId,
        //     // userId,
        //   );
        //
        // if (oldComments?.length > 0) {
        //   await this.sendToClientByComments(
        //     socketClient,
        //     oldComments,
        //     liveId,
        //     LivestreamPlatform.YOUTUBE,
        //   );
        // }

        liveSessionId = await this.liveManagerService.createLiveSession(
          LivestreamPlatform.YOUTUBE,
          userId,
          youtubeLiveConnection.channelId,
          youtubeLiveConnection.videoId,
          youtubeLiveConnection.title,
          `https://www.youtube.com/watch?v=${youtubeLiveConnection.videoId}`,
        );
      }

      return { youtubeLiveConnection, liveSessionId };
    } catch (error) {
      this.logger.error(
        `[DEBUG_HERE] Failed to create connection for liveId: ${liveId}. Error: ${error.message} | ${error.code}`,
      );
      throw new AppException(LIVE_CHANNEL_NOT_EXIST);
    }
  }
}

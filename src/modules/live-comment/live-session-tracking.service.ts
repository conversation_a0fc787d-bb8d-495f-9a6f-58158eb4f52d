import { Inject, Injectable, OnModuleInit, forwardRef } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { RedisService } from '@shared/redis';
import { AppLogger } from '@common/logger/logger.service';
import { UserSubscriptionService } from '@module/subscription/user-subscription.service';
import { SubscriptionPrivilegeKey, SystemUser } from '@common/schemas';
import { LiveSessionRedisService } from '@module/live-comment/redis/live-session.redis';
import { Socket } from 'socket.io';
import * as AppError from '@common/exceptions/error';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class LiveSessionTrackingService implements OnModuleInit {
  private readonly trackingIntervals: Map<string, NodeJS.Timeout> = new Map();
  // Track which users are already being tracked for usage to prevent double counting
  static usersBeingTracked: Set<string> = new Set();

  constructor(
    private readonly redisService: RedisService,
    private readonly logger: AppLogger,
    @Inject(forwardRef(() => UserSubscriptionService))
    private readonly userSubscriptionService: UserSubscriptionService,
    private readonly liveSessionRedisService: LiveSessionRedisService,
    // implement system user model
    @InjectModel(SystemUser.name)
    private readonly systemUserModel: Model<SystemUser>,
  ) {
    this.logger.setContext(LiveSessionTrackingService.name);
  }

  async onModuleInit() {
    // Clear the usersBeingTracked set on startup
    LiveSessionTrackingService.usersBeingTracked.clear();
    this.logger.log('Cleared users tracking set on startup');

    // Clean up any orphaned sessions on startup
    await this.cleanupOrphanedSessions();
  }

  /**
   * Start tracking a live session for a user
   */
  async startStreamTracking(
    userId: string,
    sessionId: string,
    socketClient?: Socket,
  ): Promise<void> {
    const sessionKey = `user:${userId}:session:${sessionId}`;
    const activeSessionsKey = `user:${userId}:active_sessions`;

    // Record start time
    await this.redisService.set(`${sessionKey}:start`, Date.now().toString());

    // Add session to user's active sessions
    await this.redisService.sadd(`${activeSessionsKey}:set`, sessionId);

    // Increment active sessions counter
    await this.redisService.incr(activeSessionsKey);

    // Set heartbeat
    await this.redisService.set(
      `${sessionKey}:heartbeat`,
      Date.now().toString(),
      60,
    ); // 60 seconds TTL

    // Start periodic tracking
    this.startPeriodicTracking(userId, sessionId, socketClient);

    this.logger.log(`Started tracking session ${sessionId} for user ${userId}`);
  }

  /**
   * End tracking a live session for a user
   */
  async endStreamTracking(
    userId: string,
    sessionId: string,
    force = false,
  ): Promise<void> {
    const sessionKey = `user:${userId}:session:${sessionId}`;
    const activeSessionsKey = `user:${userId}:active_sessions`;

    // Get start time
    const startTimeStr = await this.redisService.get(`${sessionKey}:start`);
    if (!startTimeStr) {
      this.logger.warn(
        `No start time found for session ${sessionId} of user ${userId}`,
      );
      return;
    }

    // Calculate final duration and update total
    const startTime = parseInt(startTimeStr);
    const duration = Math.ceil((Date.now() - startTime) / (1000 * 60)); // in minutes, rounded up
    await this.updateStreamDuration(userId, duration, force);

    // Remove session from user's active sessions
    await this.redisService.srem(`${activeSessionsKey}:set`, sessionId);

    // Decrement active sessions counter
    await this.redisService.decr(activeSessionsKey);

    // Clean up
    await this.redisService.del(`${sessionKey}:start`);
    await this.redisService.del(`${sessionKey}:heartbeat`);

    // Clear interval if exists
    const intervalKey = `${userId}:${sessionId}`;
    if (this.trackingIntervals.has(intervalKey)) {
      clearInterval(this.trackingIntervals.get(intervalKey));
      this.trackingIntervals.delete(intervalKey);
    }

    // Check if this was the last active session for this user
    const activeSessionsCount = parseInt(
      (await this.redisService.get(activeSessionsKey)) || '0',
    );

    if (activeSessionsCount === 0) {
      // If this was the last session, ensure user is removed from tracking set
      LiveSessionTrackingService.usersBeingTracked.delete(userId);
      this.logger.debug(
        `Removed user ${userId} from tracking set (no active sessions)`,
      );
    }

    this.logger.log(
      `Ended tracking session ${sessionId} for user ${userId} (duration: ${duration} minutes)`,
    );
  }

  /**
   * Update heartbeat for a session to keep it alive
   */
  async updateHeartbeat(userId: string, sessionId: string): Promise<void> {
    const sessionKey = `user:${userId}:session:${sessionId}`;
    await this.redisService.set(
      `${sessionKey}:heartbeat`,
      Date.now().toString(),
      60,
    ); // 60 seconds TTL
  }

  /**
   * Check if a user can start a new live session
   */
  async canStartNewSession(userId: string): Promise<boolean> {
    // Get user's subscription
    const subscription = await this.userSubscriptionService.getUserSubscription(
      userId,
    );
    if (!subscription) {
      this.logger.warn(`User ${userId} has no subscription`);
      return false; // No subscription means no access
    }

    const systemUser = await this.systemUserModel.findOne({
      id: userId,
    });

    const results = await this.userSubscriptionService.getUserPrivilegeByKey(
      userId,
      SubscriptionPrivilegeKey.TRIAL_DAYS,
    );

    if (results && results.unlimited) {
      this.logger.log(
        `User ${userId} has unlimited privileges for starting sessions`,
      );
      return true;
    }

    const diffDays = dayjs().diff(dayjs(systemUser.activeAt), 'day');
    if (diffDays <= results.limit) {
      this.logger.warn(
        `User ${userId} is still within their trial period, allowing session start`,
      );
      return true; // Still within trial period
    }

    return false;
    // Get current active sessions count
    // const activeSessionsCount = parseInt(
    //   (await this.redisService.get(`user:${userId}:active_sessions`)) || '0',
    // );

    // Check against limit
    // return !this.userSubscriptionService.hasUserReachedPrivilegeLimitFromSubscription(
    //   subscription,
    //   SubscriptionPrivilegeKey.CONCURRENT_LIVE_SESSIONS,
    //   activeSessionsCount,
    // );
  }

  /**
   * Check if a user can stream for more minutes
   */
  async canStreamMoreMinutes(
    userId: string,
    additionalMinutes = 1,
  ): Promise<boolean> {
    // Get user's subscription
    const subscription = await this.userSubscriptionService.getUserSubscription(
      userId,
    );
    if (!subscription) {
      return false; // No subscription means no access
    }

    // Get current usage for today
    const currentUsage = await this.getStreamingMinutesUsed(userId);

    // Check if user can stream for additional minutes
    return !this.userSubscriptionService.hasUserReachedPrivilegeLimitFromSubscription(
      subscription,
      SubscriptionPrivilegeKey.LIVE_STREAM_DURATION,
      currentUsage + additionalMinutes,
    );
  }

  /**
   * Get the current active sessions count for a user
   */
  async getActiveSessionsCount(userId: string): Promise<number> {
    return parseInt(
      (await this.redisService.get(`user:${userId}:active_sessions`)) || '0',
    );
  }

  /**
   * Get the total streaming minutes used by a user for today
   */
  async getStreamingMinutesUsed(userId: string): Promise<number> {
    const today = dayjs().format('YYYY-MM-DD');
    const key = `user:${userId}:stream:duration:daily:${today}`;
    return parseInt((await this.redisService.get(key)) || '0');
  }

  /**
   * Get the remaining streaming minutes for a user based on today's usage
   */
  async getRemainingStreamingMinutes(userId: string): Promise<number | null> {
    // Get user's subscription
    const subscription = await this.userSubscriptionService.getUserSubscription(
      userId,
    );
    if (!subscription) {
      return 0; // No subscription means no minutes
    }

    // Get the limit
    const limit =
      this.userSubscriptionService.getUserPrivilegeLimitFromSubscription(
        subscription,
        SubscriptionPrivilegeKey.LIVE_STREAM_DURATION,
      );

    // If unlimited, return null
    if (limit === null) {
      return null;
    }

    // Get current usage for today
    const used = await this.getStreamingMinutesUsed(userId);

    // Calculate remaining
    return Math.max(0, limit - used);
  }

  /**
   * Start periodic tracking for a session
   */
  private startPeriodicTracking(
    userId: string,
    sessionId: string,
    socketClient?: Socket,
  ): void {
    const intervalKey = `${userId}:${sessionId}`;
    const sessionKey = `user:${userId}:session:${sessionId}`;

    // Set up interval (every minute)
    const interval = setInterval(async () => {
      try {
        // Check if session still exists (via heartbeat)
        const heartbeat = await this.redisService.get(
          `${sessionKey}:heartbeat`,
        );
        if (!heartbeat) {
          this.logger.warn(
            `Heartbeat missing for session ${sessionId} of user ${userId}, ending tracking`,
          );
          await this.endStreamTracking(userId, sessionId);
          await this.liveSessionRedisService.deleteSession(sessionId);
          if (socketClient) {
            await this.liveSessionRedisService.removeSocketSessions(
              socketClient.id,
            );

            socketClient.emit('live_error', {
              errorCode: AppError.SESSION_INACTIVE.errorCode,
              message: AppError.SESSION_INACTIVE.errorMessage,
            });
          }
          return;
        }

        // Only update duration if this is the first active session for this user
        // or if we're not already tracking this user in this minute
        const isFirstToTrack = !this.isUserBeingTracked(userId);
        if (isFirstToTrack) {
          // Mark this user as being tracked
          LiveSessionTrackingService.usersBeingTracked.add(userId);

          // Update duration (1 minute)
          await this.updateStreamDuration(userId, 1);

          // Schedule removal from tracking set after processing
          // This ensures the user can be tracked again in the next minute
          setTimeout(() => {
            LiveSessionTrackingService.usersBeingTracked.delete(userId);
          }, Date.now() - parseInt(heartbeat) + 1000); // Small delay to prevent race conditions

          this.logger.debug(
            `Updated streaming duration for user ${userId} (1 minute)`,
          );
        } else {
          this.logger.debug(
            `Skipped duplicate duration update for user ${userId} (already being tracked)`,
          );
        }

        // Check if user has reached their limit
        if (!(await this.canStreamMoreMinutes(userId, 1))) {
          this.logger.log(
            `User ${userId} has reached their streaming limit, ending session ${sessionId}`,
          );
          await this.endStreamTracking(userId, sessionId, true);
          await this.liveSessionRedisService.deleteSession(sessionId);
          if (socketClient) {
            await this.liveSessionRedisService.removeSocketSessions(
              socketClient.id,
            );
            socketClient.emit('live_error', {
              errorCode: AppError.STREAMING_LIMIT_REACHED.errorCode,
              message: AppError.STREAMING_LIMIT_REACHED.errorMessage,
            });
          }
        }
      } catch (error) {
        this.logger.error(
          `Error in periodic tracking for session ${sessionId} of user ${userId}`,
          error.stack,
        );
      }
    }, 60000); // 1 minute

    // Store interval reference for cleanup
    this.trackingIntervals.set(intervalKey, interval);
  }

  /**
   * Check if a user is currently being tracked for usage
   * @param userId The user ID to check
   * @returns Whether the user is currently being tracked
   */
  isUserBeingTracked(userId: string): boolean {
    return LiveSessionTrackingService.usersBeingTracked.has(userId);
  }

  /**
   * Update the stream duration counter for a user
   */
  private async updateStreamDuration(
    userId: string,
    minutes: number,
    force = false,
  ): Promise<void> {
    const today = dayjs().format('YYYY-MM-DD');
    const key = `user:${userId}:stream:duration:daily:${today}`;

    if (force) {
      await this.redisService.set(key, minutes);
    } else {
      // Increment duration counter
      await this.redisService.incrby(key, minutes);
    }

    // Set expiration if not already set (24 hours + buffer)
    await this.redisService.expire(key, 86400 + 3600);
  }

  /**
   * Clean up any orphaned sessions (e.g., after server crash)
   */
  public async cleanupOrphanedSessions(): Promise<void> {
    try {
      // Find all active session keys
      const sessionKeys = await this.redisService.keys(
        'user:*:session:*:start',
      );

      for (const key of sessionKeys) {
        // Extract user ID and session ID from key
        const parts = key.split(':');
        if (parts.length >= 4) {
          const userId = parts[1];
          const sessionId = parts[3];

          // Check if heartbeat exists
          const heartbeatKey = `user:${userId}:session:${sessionId}:heartbeat`;
          const heartbeat = await this.redisService.get(heartbeatKey);

          if (!heartbeat) {
            // No heartbeat, session is orphaned
            this.logger.warn(
              `Found orphaned session ${sessionId} for user ${userId}, cleaning up`,
            );
            await this.endStreamTracking(userId, sessionId);
            await this.liveSessionRedisService.deleteSession(sessionId);
          }
        }
      }

      this.logger.log('Orphaned session cleanup completed');
    } catch (error) {
      this.logger.error('Error cleaning up orphaned sessions', error.stack);
    }
  }
}

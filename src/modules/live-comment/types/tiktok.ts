import { BaseClientComment } from '@module/live-comment/types/index';
import {
  FollowInfo,
  UserBadge,
  UserDetails,
} from '@module/live-manager/dtos/tiktok-comment.interface';

export interface TikTokClientComment extends BaseClientComment {
  msgId: string;
  profilePictureUrl: string;
  nickName: string;
  username: string;
  message: string;
  userId: string;
}

export interface TikTokPlatformComment {
  comment: string;
  userId: string;
  secUid: string;
  uniqueId: string;
  nickname: string;
  profilePictureUrl: string;
  followRole?: number;
  userBadges?: UserBadge[];
  userSceneTypes: number[];
  userDetails: UserDetails;
  followInfo?: FollowInfo;
  isModerator?: boolean;
  isNewGifter?: boolean;
  isSubscriber?: boolean;
  topGifterRank?: any;
  gifterLevel?: number;
  teamMemberLevel?: number;
  msgId: string;
  createTime: string;
  phoneNumber?: string;
}

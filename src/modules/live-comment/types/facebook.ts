import { BaseClientComment } from '@module/live-comment/types/index';

export interface FacebookClientComment extends BaseClientComment {
  commentId: string;
  userId: string;
  name: string;
  picture: string;
  message: string;
  createTime: string;
}

export interface FacebookPlatformComment {
  profileurl: string;
  commentid: string;
  chatname: string;
  chatmessage: string;
  chatimg: string;
  profileId: string;
  createdAt: string;
}

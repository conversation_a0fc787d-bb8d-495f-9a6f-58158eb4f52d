import { Module, forwardRef } from '@nestjs/common';
import { LiveSessionTrackingService } from './live-session-tracking.service';
import { LoggerModule } from '@common/logger/logger.module';
import { RedisModule } from '@shared/redis';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LiveSessionRedisService } from './redis/live-session.redis';
import { SubscriptionModule } from '@module/subscription/subscription.module';
import { UserModule } from '@module/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Subscription,
  SubscriptionSchema,
  SystemUser,
  SystemUserSchema,
} from '@common/schemas';
import { SchemaCollectionName } from '@common/constants/schema';

@Module({
  imports: [
    LoggerModule,
    forwardRef(() => SubscriptionModule),
    RedisModule.forRootAsync({
      imports: [ConfigModule, LoggerModule],
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
    ]),
  ],
  providers: [LiveSessionTrackingService, LiveSessionRedisService],
  exports: [LiveSessionTrackingService, LiveSessionRedisService],
})
export class LiveSessionTrackingModule {}

import { BaseCommentParser } from '@module/live-comment/mappers/base-comment-parser';
import {
  FacebookClientComment,
  FacebookPlatformComment,
} from '@module/live-comment/types/facebook';
import { LivestreamPlatform } from '@common/constants';
import { Comment, CommentDocument } from '@common/schemas/comment.schema';

export class FacebookCommentMapper extends BaseCommentParser {
  fromEntityToClient(comment: CommentDocument): FacebookClientComment {
    return {
      commentId: comment.commentId || '',
      userId: comment?.platformUser?.userId || '',
      name: comment?.platformUser?.nickname || '',
      picture: comment?.platformUser?.profilePictureUrl || '',
      message: comment.message || '',
      createTime: String(+comment.createdAt / 1000),
      isOldCustomer: <PERSON><PERSON>an(comment?.platformUserId),
      hasCanceledOrder: comment?.platformUser?.hasCanceledOrder || false,
      phoneNumber: comment?.platformUser?.phoneNumber || '',
    };
  }

  fromPlatformToPlatformUser(platformComment: FacebookPlatformComment) {
    return {
      userId: platformComment.profileId,
      secUid: '',
      uniqueId: '',
      nickname: platformComment.chatname,
      profilePictureUrl: platformComment.chatimg,
      livestreamPlatform: LivestreamPlatform.FACEBOOK,
    };
  }

  fromPlatformToEntity(
    platformComment: FacebookPlatformComment,
  ): CommentDocument {
    return null as any;
  }

  getClientEventName(): string {
    return 'get-facebook-live-comment';
  }
}

import { BaseCommentParser } from '@module/live-comment/mappers/base-comment-parser';
import { LivestreamPlatform } from '@common/constants';
import {
  MessageItem,
  YoutubeClientComment,
} from '@module/live-comment/types/youtube';
import * as dayjs from 'dayjs';
import { AddChatItemAction, YTRun } from 'masterchat';
import { CommentDocument } from '@common/schemas/comment.schema';

export class YoutubeCommentMapper extends BaseCommentParser {
  fromEntityToClient(comment: CommentDocument): YoutubeClientComment {
    let message;

    try {
      message = JSON.parse(comment.message);
    } catch (e) {
      message = comment.message;
    }

    return {
      id: comment.commentId || '',
      author: {
        name: comment?.platformUser?.nickname || '',
        thumbnail: {
          url: comment.platformUser?.profilePictureUrl,
          alt: '',
        },
        channelId: comment.platformUser?.userId,
        badge: {
          thumbnail: {
            url: comment.platformUser?.profilePictureUrl,
            alt: '',
          },
          label: '',
        },
      },
      message: [
        {
          text: message ? String(message) : '',
        },
      ],
      isMembership: false,
      isVerified: false,
      isOwner: false,
      isModerator: false,
      timestamp: dayjs(comment.createdAt).toDate(),
      isOldCustomer: Boolean(comment?.platformUserId),
      hasCanceledOrder: comment?.platformUser?.hasCanceledOrder || false,
      phoneNumber: comment?.platformUser?.phoneNumber || '',
    };
  }

  fromPlatformToPlatformUser(platformComment: AddChatItemAction) {
    return {
      userId: platformComment.authorChannelId,
      secUid: '',
      uniqueId: '',
      nickname: platformComment.authorName,
      profilePictureUrl: platformComment.authorPhoto,
      livestreamPlatform: LivestreamPlatform.YOUTUBE,
    };
  }

  fromPlatformToEntity(platformComment: AddChatItemAction): CommentDocument {
    return null as any;
    // return {
    //   msgId: platformComment.id,
    //   // user: {
    //   //   userId: platformComment.authorChannelId,
    //   //   nickname: platformComment.authorName,
    //   //   profilePictureUrl: platformComment.authorPhoto,
    //   //   secUid: '',
    //   //   uniqueId: '',
    //   //   livestreamPlatform: LivestreamPlatform.YOUTUBE,
    //   // },
    //   comment: JSON.stringify(this.parseMessages(platformComment.message)),
    //   createTime: String(+platformComment.timestampUsec / 1000),
    // };
  }

  parseMessages(runs: YTRun[]): MessageItem[] {
    return runs.map((run: YTRun): MessageItem => {
      if ('text' in run) {
        return run;
      } else {
        const thumbnail = run.emoji.image.thumbnails.shift();
        const isCustomEmoji = Boolean(run.emoji.isCustomEmoji);
        const shortcut = run.emoji.shortcuts ? run.emoji.shortcuts[0] : '';
        return {
          url: thumbnail ? thumbnail.url : '',
          alt: shortcut,
          isCustomEmoji: isCustomEmoji,
          emojiText: isCustomEmoji ? shortcut : run.emoji.emojiId,
        };
      }
    });
  }

  fromYTCommentToText(runs: YTRun[]): string {
    const messages = this.parseMessages(runs);
    return messages
      .map(message => {
        if ('text' in message) {
          return message.text;
        } else {
          return message.emojiText;
        }
      })
      .join('');
  }

  getClientEventName(): string {
    return 'get-youtube-live-comment';
  }
}

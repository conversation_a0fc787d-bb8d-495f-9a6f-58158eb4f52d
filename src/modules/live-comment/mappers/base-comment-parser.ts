import { PlatformUser } from '@common/schemas';
import { CommentDocument } from '@common/schemas/comment.schema';

export abstract class BaseCommentParser {
  abstract fromEntityToClient(comment: CommentDocument);

  abstract fromPlatformToEntity(platformComment: any): CommentDocument;

  abstract fromPlatformToPlatformUser(
    platformComment: any,
  ): Omit<
    PlatformUser,
    'id' | 'hasCanceledOrder' | 'phoneNumber' | 'createdAt'
  >;

  abstract getClientEventName(): string;
}

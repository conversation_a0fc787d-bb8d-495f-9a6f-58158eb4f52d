import { BaseCommentParser } from '@module/live-comment/mappers/base-comment-parser';
import { LivestreamPlatform } from '@common/constants';
import {
  TikTokClientComment,
  TikTokPlatformComment,
} from '@module/live-comment/types/tiktok';
import { CommentDocument } from '@common/schemas/comment.schema';

export class TikTokCommentMapper extends BaseCommentParser {
  fromEntityToClient(comment: CommentDocument): TikTokClientComment {
    return {
      msgId: comment.commentId,
      profilePictureUrl: comment?.platformUser?.profilePictureUrl || '',
      nickName: comment?.platformUser?.nickname || '',
      username: comment?.platformUser?.uniqueId || '',
      message: comment.message || '',
      userId: comment?.platformUser?.userId || '',
      isOldCustomer: Boolean(comment?.platformUserId),
      hasCanceledOrder: comment?.platformUser?.hasCanceledOrder || false,
      phoneNumber: comment?.platformUser?.phoneNumber || '',
    };
  }

  fromPlatformToPlatformUser(platformComment: TikTokPlatformComment) {
    return {
      userId: platformComment.userId,
      secUid: '',
      uniqueId: platformComment.uniqueId,
      nickname: platformComment.nickname,
      profilePictureUrl: platformComment.profilePictureUrl,
      livestreamPlatform: LivestreamPlatform.TIKTOK,
    };
  }

  fromPlatformToEntity(
    platformComment: TikTokPlatformComment,
  ): CommentDocument {
    return null as any;
    // return {
    //   msgId: platformComment.msgId,
    //   // user: {
    //   //   userId: platformComment.userId,
    //   //   nickname: platformComment.nickname,
    //   //   profilePictureUrl: platformComment.profilePictureUrl,
    //   //   secUid: platformComment.secUid,
    //   //   uniqueId: platformComment.uniqueId,
    //   //   livestreamPlatform: LivestreamPlatform.TIKTOK,
    //   // },
    //   comment: platformComment.comment,
    //   createTime: platformComment.createTime,
    // };
  }

  getClientEventName(): string {
    return 'get-tiktok-live-comment';
  }
}

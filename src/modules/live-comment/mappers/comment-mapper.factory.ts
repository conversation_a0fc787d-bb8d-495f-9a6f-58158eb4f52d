import { LivestreamPlatform } from '@common/constants';
import { TikTokCommentMapper } from '@module/live-comment/mappers/tiktok-comment.mapper';
import { YoutubeCommentMapper } from '@module/live-comment/mappers/youtube-comment.mapper';
import { FacebookCommentMapper } from '@module/live-comment/mappers/facebook-comment.mapper';

export class CommentMapperFactory {
  constructor() {}

  init(platform: LivestreamPlatform) {
    switch (platform) {
      case LivestreamPlatform.TIKTOK:
        return new TikTokCommentMapper();
      case LivestreamPlatform.YOUTUBE:
        return new YoutubeCommentMapper();
      case LivestreamPlatform.FACEBOOK:
        return new FacebookCommentMapper();
      default:
        throw new Error(`Platform ${platform} not supported`);
    }
  }
}

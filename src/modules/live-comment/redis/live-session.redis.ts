import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { RedisConnectionManagerService } from '@shared/redis/redis-connection-manager.service';
import { AppLogger } from '@common/logger/logger.service';
import { Redis } from 'ioredis';

@Injectable()
export class LiveSessionRedisService implements OnModuleInit, OnModuleDestroy {
  private readonly SESSION_PREFIX = 'live:session:';
  private readonly SOCKET_PREFIX = 'live:socket:';
  private readonly COMMENT_PREFIX = 'live:comment:';
  private readonly DISCONNECT_CHANNEL = 'live:disconnect';
  private readonly SESSION_TTL = 86400; // 24 hours in seconds
  private readonly COMMENT_TTL = 3600; // 1 hour in seconds

  // Store disconnect callbacks in memory (these can't be serialized to Redis)
  private disconnectCallbacks: Map<string, Map<string, () => void>> = new Map();

  private subClient: Redis;

  constructor(
    private readonly redisConnectionManager: RedisConnectionManagerService,
    private readonly logger: AppLogger,
  ) {
    this.logger.setContext(LiveSessionRedisService.name);
  }

  async onModuleInit() {
    // Get a dedicated subscription client
    this.subClient =
      this.redisConnectionManager.createNamedClient('live-session-sub');

    // Subscribe to disconnect events
    await this.subClient.subscribe(this.DISCONNECT_CHANNEL);

    this.subClient.on('message', async (channel, message) => {
      if (channel === this.DISCONNECT_CHANNEL) {
        try {
          const { sessionId, platformChannelId } = JSON.parse(message);
          this.logger.log(
            `Received disconnect event for session ${sessionId} and platform ${
              platformChannelId || 'all'
            }`,
          );

          // Execute local disconnect callback if it exists
          await this.executeLocalDisconnect(sessionId, platformChannelId);
        } catch (error) {
          this.logger.error(
            `Error processing disconnect message: ${error.message}`,
          );
        }
      }
    });
  }

  /**
   * Store session data in Redis using sessionId as the key
   */
  async set(sessionId: string, data: any): Promise<void> {
    try {
      const key = `${this.SESSION_PREFIX}${sessionId}`;
      await this.redisConnectionManager
        .getClient()
        .set(key, JSON.stringify(data), 'EX', this.SESSION_TTL);

      // Add socket to session mapping if socketClientId is provided
      if (data.socketClientId) {
        await this.redisConnectionManager
          .getClient()
          .sadd(
            `${this.SOCKET_PREFIX}${data.socketClientId}:sessions`,
            sessionId,
          );
        await this.redisConnectionManager
          .getClient()
          .expire(
            `${this.SOCKET_PREFIX}${data.socketClientId}:sessions`,
            this.SESSION_TTL,
          );
      }
    } catch (error) {
      this.logger.error(
        `Error storing session data for session ${sessionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get session data from Redis
   */
  async get(sessionId: string): Promise<any> {
    const key = `${this.SESSION_PREFIX}${sessionId}`;
    const data = await this.redisConnectionManager.getClient().get(key);
    if (!data) return null;

    try {
      return JSON.parse(data);
    } catch (error) {
      this.logger.error(
        `Error parsing session data for session ${sessionId}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Get all sessions for a socket client
   */
  async getSessionsBySocketId(socketClientId: string): Promise<string[]> {
    try {
      return await this.redisConnectionManager
        .getClient()
        .smembers(`${this.SOCKET_PREFIX}${socketClientId}:sessions`);
    } catch (error) {
      this.logger.error(
        `Error getting sessions for socket ${socketClientId}: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Map a disconnect function to a session and platform channel
   */
  mapDisconnect(
    sessionId: string,
    platformChannelId: string,
    disconnect: () => void,
  ): void {
    // Store the disconnect callback in memory
    if (!this.disconnectCallbacks.has(sessionId)) {
      this.disconnectCallbacks.set(sessionId, new Map());
    }

    this.disconnectCallbacks.get(sessionId).set(platformChannelId, disconnect);
  }

  /**
   * Execute a local disconnect callback
   */
  private async executeLocalDisconnect(
    sessionId: string,
    platformChannelId?: string,
  ): Promise<void> {
    // Check if we have disconnect callbacks for this session
    if (this.disconnectCallbacks.has(sessionId)) {
      if (platformChannelId) {
        // Disconnect specific platform channel
        const disconnectCallback = this.disconnectCallbacks
          .get(sessionId)
          .get(platformChannelId);
        if (disconnectCallback) {
          try {
            disconnectCallback();
            this.logger.log(
              `Executed disconnect callback for session ${sessionId} and platform ${platformChannelId}`,
            );
          } catch (e) {
            this.logger.error(
              `Error disconnecting from ${platformChannelId}:`,
              e.stack,
            );
          }
          this.disconnectCallbacks.get(sessionId).delete(platformChannelId);
        }
      } else {
        // Disconnect all platform channels
        // Convert entries to array and explicitly type it to ensure proper iteration
        const entries = Array.from(
          this.disconnectCallbacks.get(sessionId).entries(),
        ) as [string, () => void][];
        for (const entry of entries) {
          const channelId = entry[0];
          const disconnectCallback = entry[1];
          try {
            disconnectCallback();
            this.logger.log(
              `Executed disconnect callback for session ${sessionId} and platform ${channelId}`,
            );
          } catch (e) {
            this.logger.error(
              `Error disconnecting from ${channelId}:`,
              e.stack,
            );
          }
        }
        this.disconnectCallbacks.delete(sessionId);
      }
    }
  }

  /**
   * Publish a disconnect event to all servers
   */
  private async publishDisconnectEvent(
    sessionId: string,
    platformChannelId?: string,
  ): Promise<void> {
    try {
      const message = JSON.stringify({
        sessionId,
        platformChannelId,
        timestamp: Date.now(),
      });

      await this.redisConnectionManager
        .getClient()
        .publish(this.DISCONNECT_CHANNEL, message);
      this.logger.log(
        `Published disconnect event for session ${sessionId} and platform ${
          platformChannelId || 'all'
        }`,
      );
    } catch (error) {
      this.logger.error(`Error publishing disconnect event: ${error.message}`);
    }
  }

  /**
   * Delete a specific session and its associated data
   */
  async deleteSession(
    sessionId: string,
    platformChannelId?: string,
  ): Promise<void> {
    try {
      // Execute local disconnect callbacks
      await this.executeLocalDisconnect(sessionId, platformChannelId);

      // Publish disconnect event to all servers
      await this.publishDisconnectEvent(sessionId, platformChannelId);

      // If platform channel is specified and doesn't match the session's platformChannelId,
      // we don't need to delete the session
      if (platformChannelId) {
        const sessionData = await this.get(sessionId);
        if (
          sessionData &&
          sessionData.platformChannelId !== platformChannelId
        ) {
          return;
        }
      }

      // Get session data to find the socketClientId
      const sessionData = await this.get(sessionId);
      if (sessionData && sessionData.socketClientId) {
        // Remove this session from the socket's sessions set
        await this.redisConnectionManager
          .getClient()
          .srem(
            `${this.SOCKET_PREFIX}${sessionData.socketClientId}:sessions`,
            sessionId,
          );
      }

      // Delete the session
      const key = `${this.SESSION_PREFIX}${sessionId}`;
      await this.redisConnectionManager.getClient().del(key);

      // Delete the comment checker for this session
      await this.clearCommentHistory(sessionId);
    } catch (error) {
      this.logger.error(
        `Error deleting session ${sessionId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Remove all sessions associated with a socket client
   */
  async removeSocketSessions(socketClientId: string): Promise<void> {
    try {
      // Get all sessions for this socket
      const sessionIds = await this.getSessionsBySocketId(socketClientId);

      // Flush each session
      for (const sessionId of sessionIds) {
        await this.deleteSession(sessionId);
      }

      // Clean up the socket's sessions set
      await this.redisConnectionManager
        .getClient()
        .del(`${this.SOCKET_PREFIX}${socketClientId}:sessions`);
    } catch (error) {
      this.logger.error(
        `Error removing sessions for socket ${socketClientId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Clear comment history data for a session
   */
  async clearCommentHistory(sessionId: string): Promise<void> {
    try {
      const key = `${this.COMMENT_PREFIX}${sessionId}`;
      await this.redisConnectionManager.getClient().del(key);
    } catch (error) {
      this.logger.error(
        `Error clearing comment history for session ${sessionId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Clean up all data for a socket client
   * @deprecated Use removeSocketSessions instead
   */
  async cleanup(socketClientId: string): Promise<void> {
    await this.removeSocketSessions(socketClientId);
  }

  /**
   * Check if a comment has been sent to a session
   */
  async hasSentComment(sessionId: string, commentId: string): Promise<boolean> {
    try {
      const key = `${this.COMMENT_PREFIX}${sessionId}`;
      return (
        (await this.redisConnectionManager
          .getClient()
          .sismember(key, commentId)) === 1
      );
    } catch (error) {
      this.logger.error(
        `Error checking if comment ${commentId} was sent to session ${sessionId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Add a sent comment to the set for a session
   */
  async addSentComment(sessionId: string, commentId: string): Promise<void> {
    try {
      const key = `${this.COMMENT_PREFIX}${sessionId}`;
      await this.redisConnectionManager.getClient().sadd(key, commentId);
      await this.redisConnectionManager
        .getClient()
        .expire(key, this.COMMENT_TTL);
    } catch (error) {
      this.logger.error(
        `Error adding comment ${commentId} to session ${sessionId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Clear all data from Redis
   */
  async clearAll(): Promise<void> {
    try {
      // Clear all session data
      const sessionKeys = await this.redisConnectionManager
        .getClient()
        .keys(`${this.SESSION_PREFIX}*`);
      if (sessionKeys.length > 0) {
        await Promise.all(
          sessionKeys.map(key =>
            this.redisConnectionManager.getClient().del(key),
          ),
        );
      }

      // Clear all socket mappings
      const socketKeys = await this.redisConnectionManager
        .getClient()
        .keys(`${this.SOCKET_PREFIX}*`);
      if (socketKeys.length > 0) {
        await Promise.all(
          socketKeys.map(key =>
            this.redisConnectionManager.getClient().del(key),
          ),
        );
      }

      // Clear all comment checker data
      const commentKeys = await this.redisConnectionManager
        .getClient()
        .keys(`${this.COMMENT_PREFIX}*`);
      if (commentKeys.length > 0) {
        await Promise.all(
          commentKeys.map(key =>
            this.redisConnectionManager.getClient().del(key),
          ),
        );
      }

      // Clear disconnect callbacks
      this.disconnectCallbacks.clear();

      this.logger.log('Cleared all Redis data for live sessions');
    } catch (error) {
      this.logger.error(
        `Error clearing all Redis data: ${error.message}`,
        error.stack,
      );
    }
  }

  async onModuleDestroy() {
    // Clean up subscription
    if (this.subClient) {
      await this.subClient.unsubscribe(this.DISCONNECT_CHANNEL);
      this.logger.log('Unsubscribed from disconnect channel');
    }
  }
}

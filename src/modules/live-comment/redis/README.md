# Redis-Based Live Session Management

This directory contains the Redis-based implementation for live session management, replacing the in-memory `LiveSessionMemcached` class.

## Why Redis?

The original `LiveSessionMemcached` implementation stored all session data in memory, which has several limitations:

1. **No Multi-Server Support**: Data is isolated to a single server instance, making it impossible to scale horizontally.
2. **No Persistence**: All data is lost when the server restarts.
3. **Memory Limitations**: As the number of sessions grows, memory usage can become a problem.

Redis solves these issues by providing:

1. **Centralized Storage**: All server instances connect to the same Redis instance.
2. **Optional Persistence**: Redis can be configured to persist data to disk.
3. **Memory Management**: Redis has built-in mechanisms for memory management.
4. **Atomic Operations**: Redis provides atomic operations for counters and other data structures.

## Components

### LiveSessionRedisService

This service provides the core functionality for storing and retrieving session data in Redis. It includes methods for:

- Storing and retrieving session data
- Managing disconnect callbacks
- Tracking sent comments
- Cleaning up sessions

### LiveSessionMemcachedCompat

This compatibility layer provides the same static API as the original `LiveSessionMemcached` class but delegates to the Redis-based implementation. This allows for a gradual migration from the in-memory solution to Redis.

## Migration Plan

The migration from `LiveSessionMemcached` to the Redis-based solution will happen in phases:

### Phase 1: Dual-Write and Read-Old (Current Implementation)

- The compatibility layer writes to both in-memory and Redis
- It reads primarily from in-memory with fallback to Redis
- This ensures backward compatibility while building up data in Redis

### Phase 2: Read-New and Write-Both

- Update the compatibility layer to read primarily from Redis with fallback to in-memory
- Continue writing to both systems
- This tests Redis reads in production while maintaining the safety net

### Phase 3: Write-New and Read-New

- Update the compatibility layer to only write to Redis
- Continue reading from Redis
- The in-memory data becomes stale but is still available as a fallback

### Phase 4: Direct Redis Usage

- Gradually update all code that uses `LiveSessionMemcached` to use `LiveSessionRedisService` directly
- This eliminates the compatibility layer overhead

### Phase 5: Remove Compatibility Layer

- Once all code has been updated, remove the compatibility layer

## Usage

### Using the Compatibility Layer (During Migration)

During the migration, you can continue to use the static methods of `LiveSessionMemcachedCompat` just as you would use `LiveSessionMemcached`:

```typescript
// Store session data
LiveSessionMemcachedCompat.set(socketClient.id, {
  user: { id: userId, username },
  sessionId,
  platformChannelId,
});

// Get session data
const sessionData = LiveSessionMemcachedCompat.get(socketClient.id);

// Map disconnect callback
LiveSessionMemcachedCompat.mapDisconnect(socketClient.id, platformChannelId, () => {
  connection.disconnect();
});

// Check if comment has been sent
if (LiveSessionMemcachedCompat.hasSentComment(socketClient.id, commentId)) {
  return;
}

// Add sent comment
LiveSessionMemcachedCompat.addSentComment(socketClient.id, commentId);

// Flush session
LiveSessionMemcachedCompat.flush(socketClient.id);
```

### Using LiveSessionRedisService Directly (After Migration)

After migration, you should inject and use `LiveSessionRedisService` directly:

```typescript
@Injectable()
export class YourService {
  constructor(private readonly liveSessionRedisService: LiveSessionRedisService) {}

  async someMethod() {
    // Store session data
    await this.liveSessionRedisService.set(socketClient.id, {
      user: { id: userId, username },
      sessionId,
      platformChannelId,
    });

    // Get session data
    const sessionData = await this.liveSessionRedisService.get(socketClient.id);

    // Map disconnect callback
    this.liveSessionRedisService.mapDisconnect(socketClient.id, platformChannelId, () => {
      connection.disconnect();
    });

    // Check if comment has been sent
    if (await this.liveSessionRedisService.hasSentComment(socketClient.id, commentId)) {
      return;
    }

    // Add sent comment
    await this.liveSessionRedisService.addSentComment(socketClient.id, commentId);

    // Flush session
    await this.liveSessionRedisService.flush(socketClient.id);
  }
}
```

## Configuration

The Redis connection is configured through environment variables:

- `REDIS_HOST`: Redis server hostname (default: 'localhost')
- `REDIS_PORT`: Redis server port (default: 6379)
- `REDIS_PASSWORD`: Redis server password (default: '')
- `REDIS_PREFIX`: Key prefix for Redis keys (default: 'livestream:')

These can be set in your `.env` file or through your deployment configuration.

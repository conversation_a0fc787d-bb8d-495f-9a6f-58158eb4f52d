import { DatabaseModule } from '@common/config/database.module';
import { Module } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { LiveManagerModule } from './live-manager/live-manager.module';
import { PlatformUserModule } from '@module/platform-user/platform-user.module';
import { APP_GUARD } from '@nestjs/core';
import { PermissionsGuard } from '@common/guards';
import { JwtService } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { SystemUser, SystemUserSchema } from '@common/schemas';
import { SystemConfigModule } from '@module/system-config/system-config.module';
import { ContactModule } from './contact/contact.module';
import { HealthModule } from '@module/health/health.module';
import { FacebookManagerModule } from '@module/facebook-manager/facebook-manager.module';
import { PlatformChannelModule } from '@module/platform-channel/platform-channel.module';
import { SchemaCollectionName } from '@common/constants/schema';
import { UserModule } from '@module/user/user.module';
import { TestModule } from '@module/test/test.module';
import { SubscriptionModule } from '@module/subscription/subscription.module';
import { FeatureFlagModule } from '@module/feature-flag/feature-flag.module';
import { StreamHubModule } from '@module/stream-hub/stream-hub.module';

@Module({
  imports: [
    DatabaseModule,
    LiveManagerModule,
    SystemConfigModule,
    AuthModule,
    PlatformUserModule,
    HealthModule,
    FacebookManagerModule,
    PlatformChannelModule,
    UserModule,
    SubscriptionModule,
    TestModule,
    FeatureFlagModule,
    ContactModule,
    StreamHubModule,
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
    ]),
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard,
    },
    JwtService,
  ],
})
export class ApiModule {}

import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SystemConfig } from '@common/schemas/system-config.schema';

@Injectable()
export class SystemConfigService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(SystemConfig.name)
    private systemConfigModel: Model<SystemConfig>,
  ) {
    this.logger.setContext(SystemConfigService.name);
  }

  getSystemConfig(key: string): Promise<SystemConfig> {
    return this.systemConfigModel.findOne({ key }).exec();
  }

  getSystemConfigs(keys: string[]): Promise<SystemConfig[]> {
    return this.systemConfigModel
      .find({
        key: {
          $in: keys,
        },
      })
      .exec();
  }

  async createSystemConfig(key: string, value: string): Promise<SystemConfig> {
    const systemConfig = await this.systemConfigModel
      .findOne({
        key,
      })
      .exec();
    if (systemConfig) {
      systemConfig.value = value;
      return systemConfig.save();
    } else {
      const newSystemConfig = new this.systemConfigModel({
        key,
        value,
      });
      return newSystemConfig.save();
    }
  }

  async shouldPrioritizeBackupTiktok(): Promise<boolean> {
    const tiktokBackupPriority = await this.getSystemConfig(
      'tiktok_backup_priority',
    );

    return tiktokBackupPriority?.value === '1';
  }

  async strictChannel(): Promise<boolean> {
    const strictChannel = await this.getSystemConfig('strict_channel');

    return strictChannel?.value === '1';
  }
}

import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  SystemConfig,
  SystemConfigSchema,
  SystemUser,
  SystemUserSchema,
} from '@common/schemas';
import { SystemConfigService } from './system-config.service';
import { PlatformUserModule } from '@module/platform-user/platform-user.module';
import { SystemConfigController } from '@module/system-config/system-config.controller';
import { SchemaCollectionName } from '@common/constants/schema';
import { AdminGuard } from '@common/guards/admin.guard';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
      {
        name: SystemConfig.name,
        schema: SystemConfigSchema,
        collection: SchemaCollectionName.SystemConfig,
      },
    ]),
    PlatformUserModule,
  ],
  controllers: [SystemConfigController],
  providers: [SystemConfigService, AdminGuard],
  exports: [SystemConfigService, AdminGuard],
})
export class SystemConfigModule {}

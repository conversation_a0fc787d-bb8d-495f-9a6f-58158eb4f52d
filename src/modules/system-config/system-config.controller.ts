import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SystemConfigService } from './system-config.service';
import { AdminGuard } from '@common/guards/admin.guard';

@ApiTags('SystemConfig')
@Controller('/system-config')
export class SystemConfigController {
  constructor(private readonly systemConfigService: SystemConfigService) {}

  @Get(':key')
  @UseGuards(AdminGuard)
  async getSystemConfig(@Param('key') key: string): Promise<any> {
    return this.systemConfigService.getSystemConfig(key);
  }

  @Get('')
  @UseGuards(AdminGuard)
  async getSystemConfigs(@Query('keys') keys: string[]): Promise<any> {
    return this.systemConfigService.getSystemConfigs(keys);
  }

  @Post()
  @UseGuards(AdminGuard)
  async createSystemConfig(@Body() body: any): Promise<any> {
    return this.systemConfigService.createSystemConfig(body.key, body.value);
  }
}

import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SearchPlatformUserDto } from './dtos/search-platform-user.dto';
import { AppException } from '@common/exceptions/app-exception';
import { USER_NOT_EXIST } from '@common/exceptions/error';
import { PlatformUser } from '@common/schemas';
import { UpdatePlatformUserCanceledOrderDto } from '@module/platform-user/dtos/update-platform-user-canceled-order.dto';

@Injectable()
export class PlatformUserService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(PlatformUser.name)
    private platformUserModel: Model<PlatformUser>,
  ) {
    this.logger.setContext(PlatformUserService.name);
  }

  async getUsers(
    searchUser: SearchPlatformUserDto,
  ): Promise<{ total: number; users: PlatformUser[] }> {
    const { limit, page, userId, nickname, phoneNumber, uniqueId } = searchUser;

    const query: any = {};

    if (userId) {
      query.userId = { $regex: new RegExp(userId, 'i') };
    }
    if (nickname) {
      query.nickname = { $regex: new RegExp(nickname, 'i') };
    }
    if (phoneNumber) {
      query.phoneNumber = { $regex: new RegExp(phoneNumber, 'i') };
    }
    if (uniqueId) {
      query.uniqueId = { $regex: new RegExp(uniqueId, 'i') };
    }

    const total = await this.platformUserModel.countDocuments(query).exec();

    const users = await this.platformUserModel
      .find(query)
      .limit(limit)
      .skip((page - 1) * limit)
      .exec();

    return {
      total,
      users,
    };
  }

  async updateUserHasCanceledOrder(
    userId: string,
    { hasCanceledOrder }: UpdatePlatformUserCanceledOrderDto,
  ): Promise<PlatformUser | null> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new AppException(USER_NOT_EXIST);
    }

    const updatedUser = await this.platformUserModel
      .findOneAndUpdate({ userId: userId }, { hasCanceledOrder }, { new: true })
      .exec();

    return updatedUser;
  }

  async updatePhoneNumberByUserId(
    userId: string,
    newPhoneNumber: string,
  ): Promise<PlatformUser | null> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new AppException(USER_NOT_EXIST);
    }

    const updatedUser = await this.platformUserModel
      .findOneAndUpdate(
        { userId: userId },
        { phoneNumber: newPhoneNumber },
        { new: true },
      )
      .exec();

    return updatedUser;
  }

  async removePhoneNumberByUserId(
    userId: string,
  ): Promise<PlatformUser | null> {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new AppException(USER_NOT_EXIST);
    }

    return await this.platformUserModel
      .findOneAndUpdate({ userId: userId }, { phoneNumber: '' }, { new: true })
      .exec();
  }

  async getUserById(userId: string): Promise<PlatformUser | null> {
    return this.platformUserModel.findOne({ userId }).exec();
  }

  async getUsersByIds(userIds: string[]): Promise<PlatformUser[]> {
    return this.platformUserModel.find({ userId: { $in: userIds } }).exec();
  }

  async saveUser(user: PlatformUser): Promise<PlatformUser> {
    const newUser = await this.platformUserModel.create(user);
    await newUser.save();
    return newUser;
  }

  upsertUser(
    data: Omit<PlatformUser, 'id' | 'hasCanceledOrder' | 'createdAt'>,
  ): Promise<PlatformUser> {
    return this.platformUserModel.findOneAndUpdate(
      {
        userId: data.userId,
        livestreamPlatform: data.livestreamPlatform,
      },
      {
        $setOnInsert: {
          userId: data.userId,
          uniqueId: data.uniqueId,
          secUid: data.secUid,
          nickname: data.nickname,
          profilePictureUrl: data.profilePictureUrl,
          livestreamPlatform: data.livestreamPlatform,
        },
      },
      {
        upsert: true,
        new: true,
      },
    );
  }
}

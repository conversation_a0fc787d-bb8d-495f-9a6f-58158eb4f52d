import { LivestreamPlatform } from '@common/constants';
import { ApiProperty } from '@nestjs/swagger';

export class PlatformUserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  nickname: string;

  @ApiProperty()
  username: string;

  @ApiProperty({ type: Boolean })
  hasCanceledOrder: boolean;

  @ApiProperty({ enum: LivestreamPlatform, enumName: 'LivestreamPlatform' })
  livestreamPlatform: LivestreamPlatform;

  @ApiProperty()
  profilePictureUrl: string;

  @ApiProperty()
  phoneNumber: string;

  @ApiProperty({ type: Date })
  createdAt: Date;
}

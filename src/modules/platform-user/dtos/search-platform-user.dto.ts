import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';

export class SearchPlatformUserDto {
  @ApiPropertyOptional({
    description: 'Number of items per page',
    default: 20,
    type: 'string',
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit = 20;

  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
    type: 'string',
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page = 1;

  @ApiPropertyOptional({
    description: 'User ID',
    type: 'string',
  })
  @IsOptional()
  userId: string;

  @ApiPropertyOptional({
    description: 'User nickname',
    type: 'string',
  })
  @IsOptional()
  nickname: string;

  @ApiPropertyOptional({
    description: 'Phone number',
    type: 'string',
  })
  @IsOptional()
  phoneNumber: string;

  @ApiPropertyOptional({
    description: 'User unique ID',
    type: 'string',
  })
  @IsOptional()
  uniqueId: string;
}

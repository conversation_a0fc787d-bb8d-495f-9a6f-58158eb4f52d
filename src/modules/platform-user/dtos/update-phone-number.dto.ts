import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsPhoneNumber, Matches } from 'class-validator';

export class UpdatePhoneNumberDto {
  @ApiProperty({
    description: 'Phone number',
    type: 'string',
  })
  @IsNotEmpty()
  @IsPhoneNumber('VN', { message: 'Invalid Vietnamese phone number' })
  @Matches(/^\d+$/, { message: 'Phone number must contain only digits' })
  phoneNumber: string;
}

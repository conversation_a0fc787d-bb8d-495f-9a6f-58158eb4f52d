import { LoggerModule } from '@common/logger/logger.module';
import { PlatformUser, PlatformUserSchema } from '@common/schemas';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PlatformUserController } from './platform-user.controller';
import { PlatformUserService } from './platform-user.service';
import { SchemaCollectionName } from '@common/constants/schema';

@Module({
  imports: [
    LoggerModule,
    MongooseModule.forFeature([
      {
        name: PlatformUser.name,
        schema: PlatformUserSchema,
        collection: SchemaCollectionName.PlatformUser,
      },
    ]),
  ],
  controllers: [PlatformUserController],
  providers: [PlatformUserService],
  exports: [PlatformUserService],
})
export class PlatformUserModule {}

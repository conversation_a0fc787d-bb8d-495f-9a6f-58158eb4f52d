import { Body, Controller, Get, Param, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PlatformUserService } from './platform-user.service';
import { SearchPlatformUserDto } from './dtos/search-platform-user.dto';
import { UpdatePhoneNumberDto } from './dtos/update-phone-number.dto';
import { Auth } from '@common/decorators';
import { UpdatePlatformUserCanceledOrderDto } from '@module/platform-user/dtos/update-platform-user-canceled-order.dto';

@ApiTags('Live User')
@Controller('/user')
export class PlatformUserController {
  constructor(private readonly userService: PlatformUserService) {}

  @Auth()
  @Get('list')
  async getLiveSession(@Query() searchUser: SearchPlatformUserDto) {
    return this.userService.getUsers(searchUser);
  }

  @Auth()
  @Put('/:userId/update-phone-number')
  async updatePhoneNumber(
    @Param('userId') userId: string,
    @Body() bodyData: UpdatePhoneNumberDto,
  ) {
    return this.userService.updatePhoneNumberByUserId(
      userId,
      bodyData.phoneNumber,
    );
  }

  @Auth()
  @Put('/:userId/remove-phone-number')
  async removePhoneNumber(@Param('userId') userId: string) {
    return this.userService.removePhoneNumberByUserId(userId);
  }

  @Auth()
  @Put('/:userId/canceled-order')
  async updateUserHasCanceledOrder(
    @Param('userId') userId: string,
    @Body() body: UpdatePlatformUserCanceledOrderDto,
  ) {
    return this.userService.updateUserHasCanceledOrder(userId, body);
  }
}

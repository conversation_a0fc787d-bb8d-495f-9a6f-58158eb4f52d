import { Injectable } from '@nestjs/common';
import { RedisService } from '@shared/redis/redis.service';
import { AppLogger } from '@common/logger/logger.service';
import { FeatureFlagDto } from './dtos/feature-flag.dto';

@Injectable()
export class FeatureFlagCacheService {
  private readonly CACHE_PREFIX = 'feature-flag:';
  private readonly ALL_FLAGS_KEY = 'feature-flag:all-active';
  private readonly CACHE_TTL = 3600; // 1 hour in seconds

  constructor(
    private readonly redisService: RedisService,
    private readonly logger: AppLogger,
  ) {
    this.logger.setContext(FeatureFlagCacheService.name);
  }

  /**
   * Get a feature flag from cache by key
   */
  async getByKey(key: string): Promise<FeatureFlagDto | null> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}key:${key}`;
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        this.logger.debug(`Cache hit for feature flag key: ${key}`);
        return JSON.parse(cachedData);
      }

      this.logger.debug(`Cache miss for feature flag key: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(
        `Error getting feature flag from cache: ${error.message}`,
      );
      return null; // Return null on error to fallback to database
    }
  }

  /**
   * Get all active feature flags from cache
   */
  async getAllActive(): Promise<FeatureFlagDto[] | null> {
    try {
      const cachedData = await this.redisService.get(this.ALL_FLAGS_KEY);

      if (cachedData) {
        this.logger.debug('Cache hit for all active feature flags');
        return JSON.parse(cachedData);
      }

      this.logger.debug('Cache miss for all active feature flags');
      return null;
    } catch (error) {
      this.logger.error(
        `Error getting all feature flags from cache: ${error.message}`,
      );
      return null; // Return null on error to fallback to database
    }
  }

  /**
   * Set a feature flag in cache
   */
  async setByKey(featureFlag: FeatureFlagDto): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}key:${featureFlag.key}`;
      await this.redisService.set(
        cacheKey,
        JSON.stringify(featureFlag),
        this.CACHE_TTL,
      );
      this.logger.debug(`Cached feature flag with key: ${featureFlag.key}`);
    } catch (error) {
      this.logger.error(`Error caching feature flag: ${error.message}`);
    }
  }

  /**
   * Set all active feature flags in cache
   */
  async setAllActive(featureFlags: FeatureFlagDto[]): Promise<void> {
    try {
      await this.redisService.set(
        this.ALL_FLAGS_KEY,
        JSON.stringify(featureFlags),
        this.CACHE_TTL,
      );
      this.logger.debug(`Cached ${featureFlags.length} active feature flags`);
    } catch (error) {
      this.logger.error(`Error caching all feature flags: ${error.message}`);
    }
  }

  /**
   * Invalidate cache for a specific feature flag
   */
  async invalidateByKey(key: string): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}key:${key}`;
      await this.redisService.del(cacheKey);
      // Also invalidate the all flags cache since this flag might be part of it
      await this.invalidateAllActive();
      this.logger.debug(`Invalidated cache for feature flag key: ${key}`);
    } catch (error) {
      this.logger.error(
        `Error invalidating feature flag cache: ${error.message}`,
      );
    }
  }

  /**
   * Invalidate cache for all active feature flags
   */
  async invalidateAllActive(): Promise<void> {
    try {
      await this.redisService.del(this.ALL_FLAGS_KEY);
      this.logger.debug('Invalidated cache for all active feature flags');
    } catch (error) {
      this.logger.error(
        `Error invalidating all feature flags cache: ${error.message}`,
      );
    }
  }

  /**
   * Invalidate all feature flag caches
   */
  async invalidateAll(): Promise<void> {
    try {
      // Get all keys with the feature flag prefix
      const keys = await this.redisService.keys(`${this.CACHE_PREFIX}*`);

      if (keys.length > 0) {
        // Delete all keys in a single operation
        await Promise.all(keys.map(key => this.redisService.del(key)));
        this.logger.debug(
          `Invalidated ${keys.length} feature flag cache entries`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error invalidating all feature flag caches: ${error.message}`,
      );
    }
  }
}

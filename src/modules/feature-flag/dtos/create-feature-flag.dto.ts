import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateFeatureFlagDto {
  @ApiProperty({ description: 'Name of the feature flag' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({ description: 'Description of the feature flag' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Unique key for the feature flag' })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({ description: 'Whether the feature flag is active' })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    description: 'Versions where this feature flag is restricted to',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  restrictedVersions: string[];
}

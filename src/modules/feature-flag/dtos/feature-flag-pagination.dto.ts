import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { FeatureFlagDto } from './feature-flag.dto';

export class FeatureFlagPaginationResultDto extends PaginationResultDto<FeatureFlagDto> {
  @ApiProperty({ type: [FeatureFlagDto] })
  data: FeatureFlagDto[];

  @ApiProperty({ type: PaginationMetaDto })
  meta: PaginationMetaDto;

  constructor(data: FeatureFlagDto[], meta: PaginationMetaDto) {
    super(data, meta);
  }
}

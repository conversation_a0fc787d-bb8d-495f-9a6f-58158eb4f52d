import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';

export class UpdateFeatureFlagDto {
  @ApiPropertyOptional({ description: 'Name of the feature flag' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: 'Description of the feature flag' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'Whether the feature flag is active' })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Versions where this feature flag is restricted to',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  restrictedVersions?: string[];
}

import { AppLogger } from '@common/logger/logger.service';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  FeatureFlag,
  FeatureFlagDocument,
} from '@common/schemas/feature-flag.schema';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { PaginationService } from '@shared/services/pagination.service';
import { CreateFeatureFlagDto } from './dtos/create-feature-flag.dto';
import { UpdateFeatureFlagDto } from './dtos/update-feature-flag.dto';
import { FeatureFlagDto } from './dtos/feature-flag.dto';
import { FeatureFlagPaginationResultDto } from './dtos/feature-flag-pagination.dto';
import { FeatureFlagCacheService } from './feature-flag-cache.service';

@Injectable()
export class FeatureFlagService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(FeatureFlag.name)
    private readonly featureFlagModel: Model<FeatureFlagDocument>,
    private readonly paginationService: PaginationService,
    private readonly featureFlagCacheService: FeatureFlagCacheService,
  ) {
    this.logger.setContext(FeatureFlagService.name);
  }

  async findOne(id: string): Promise<FeatureFlagDto> {
    const featureFlag = await this.featureFlagModel.findOne({ id }).exec();

    if (!featureFlag) {
      throw new NotFoundException(`Feature flag with ID ${id} not found`);
    }

    return this.toDto(featureFlag);
  }

  async findByKey(key: string): Promise<FeatureFlagDto | null> {
    // Try to get from cache first
    const cachedFeatureFlag = await this.featureFlagCacheService.getByKey(key);
    if (cachedFeatureFlag) {
      return cachedFeatureFlag;
    }

    // If not in cache, get from database
    const featureFlag = await this.featureFlagModel.findOne({ key }).exec();

    if (!featureFlag) {
      return null;
    }

    const featureFlagDto = this.toDto(featureFlag);

    // Cache the result
    await this.featureFlagCacheService.setByKey(featureFlagDto);

    return featureFlagDto;
  }

  async findAllActive(): Promise<FeatureFlagDto[]> {
    // Try to get from cache first
    const cachedFeatureFlags =
      await this.featureFlagCacheService.getAllActive();
    if (cachedFeatureFlags) {
      return cachedFeatureFlags;
    }

    // If not in cache, get from database
    const featureFlags = await this.featureFlagModel
      .find({ isActive: true })
      .exec();

    const featureFlagDtos = featureFlags.map(flag => this.toDto(flag));

    // Cache the result
    await this.featureFlagCacheService.setAllActive(featureFlagDtos);

    return featureFlagDtos;
  }

  async paginate(
    options: PaginationOptionsDto,
  ): Promise<FeatureFlagPaginationResultDto> {
    const paginationResult =
      await this.paginationService.paginate<FeatureFlagDocument>(
        this.featureFlagModel,
        {},
        options,
      );

    const dtos = paginationResult.data.map(featureFlag =>
      this.toDto(featureFlag),
    );
    return new FeatureFlagPaginationResultDto(dtos, paginationResult.meta);
  }

  async create(data: CreateFeatureFlagDto): Promise<FeatureFlagDto> {
    // Check if feature flag with the same key already exists
    const existingFeatureFlag = await this.featureFlagModel.findOne({
      key: data.key,
    });

    if (existingFeatureFlag) {
      throw new ConflictException(
        `Feature flag with key ${data.key} already exists`,
      );
    }

    const newFeatureFlag = await this.featureFlagModel.create(data);
    const featureFlagDto = this.toDto(newFeatureFlag);

    // Invalidate cache for all active feature flags since we added a new one
    await this.featureFlagCacheService.invalidateAllActive();

    return featureFlagDto;
  }

  async update(
    id: string,
    data: UpdateFeatureFlagDto,
  ): Promise<FeatureFlagDto> {
    // Get the feature flag before update to get its key
    const existingFeatureFlag = await this.featureFlagModel.findOne({ id });
    if (!existingFeatureFlag) {
      throw new NotFoundException(`Feature flag with ID ${id} not found`);
    }

    const featureFlag = await this.featureFlagModel.findOneAndUpdate(
      { id },
      { ...data, updatedAt: new Date() },
      { new: true },
    );

    const featureFlagDto = this.toDto(featureFlag);

    // Invalidate cache for this specific feature flag
    await this.featureFlagCacheService.invalidateByKey(existingFeatureFlag.key);

    // If isActive status changed, also invalidate the all active flags cache
    if (data.isActive !== undefined) {
      await this.featureFlagCacheService.invalidateAllActive();
    }

    return featureFlagDto;
  }

  async delete(id: string): Promise<FeatureFlagDto> {
    // Get the feature flag before deletion to get its key and isActive status
    const existingFeatureFlag = await this.featureFlagModel.findOne({ id });
    if (!existingFeatureFlag) {
      throw new NotFoundException(`Feature flag with ID ${id} not found`);
    }

    const featureFlag = await this.featureFlagModel.findOneAndDelete({ id });
    const featureFlagDto = this.toDto(featureFlag);

    // Invalidate cache for this specific feature flag
    await this.featureFlagCacheService.invalidateByKey(existingFeatureFlag.key);

    // If the deleted flag was active, also invalidate the all active flags cache
    if (existingFeatureFlag.isActive) {
      await this.featureFlagCacheService.invalidateAllActive();
    }

    return featureFlagDto;
  }

  toDto(featureFlag: FeatureFlagDocument): FeatureFlagDto {
    return new FeatureFlagDto({
      id: featureFlag.id,
      name: featureFlag.name,
      description: featureFlag.description,
      key: featureFlag.key,
      isActive: featureFlag.isActive,
      restrictedVersions: featureFlag.restrictedVersions,
      createdAt: featureFlag.createdAt,
      updatedAt: featureFlag.updatedAt,
    });
  }
}

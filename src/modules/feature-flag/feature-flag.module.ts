import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SchemaCollectionName } from '@common/constants/schema';
import { SharedModule } from '@shared/shared.module';
import { AdminFeatureFlagController } from './controllers/admin-feature-flag.controller';
import { FeatureFlagController } from './controllers/feature-flag.controller';
import { FeatureFlagService } from './feature-flag.service';
import { FeatureFlagCacheService } from './feature-flag-cache.service';
import { AdminGuard } from '@common/guards/admin.guard';
import { JwtModule } from '@nestjs/jwt';
import { RedisModule } from '@shared/redis';
import {
  FeatureFlag,
  FeatureFlagSchema,
} from '@common/schemas/feature-flag.schema';
import { SystemUser, SystemUserSchema } from '@common/schemas';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    SharedModule,
    RedisModule,
    MongooseModule.forFeature([
      {
        name: FeatureFlag.name,
        schema: FeatureFlagSchema,
        collection: SchemaCollectionName.FeatureFlag,
      },
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
    ]),
  ],
  controllers: [AdminFeatureFlagController, FeatureFlagController],
  providers: [FeatureFlagService, FeatureFlagCacheService, AdminGuard],
  exports: [FeatureFlagService, FeatureFlagCacheService],
})
export class FeatureFlagModule {}

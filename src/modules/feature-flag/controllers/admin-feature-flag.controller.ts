import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { FeatureFlagService } from '../feature-flag.service';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { FeatureFlagPaginationResultDto } from '../dtos/feature-flag-pagination.dto';
import { CreateFeatureFlagDto } from '../dtos/create-feature-flag.dto';
import { FeatureFlagDto } from '../dtos/feature-flag.dto';
import { UpdateFeatureFlagDto } from '../dtos/update-feature-flag.dto';
import { AdminGuard } from '@common/guards/admin.guard';
import { Auth } from '@common/decorators';

@ApiTags('Feature Flag management for Admin')
@Controller('/admin/feature-flags')
export class AdminFeatureFlagController {
  constructor(private readonly featureFlagService: FeatureFlagService) {}

  @UseGuards(AdminGuard)
  @Auth()
  @Get('/')
  @ApiOkResponse({ type: FeatureFlagPaginationResultDto })
  paginate(
    @Query() paginationOptions: PaginationOptionsDto,
  ): Promise<FeatureFlagPaginationResultDto> {
    return this.featureFlagService.paginate(paginationOptions);
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Get('/:id')
  @ApiOkResponse({ type: FeatureFlagDto })
  get(@Param('id') id: string): Promise<FeatureFlagDto> {
    return this.featureFlagService.findOne(id);
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Post('/')
  @ApiOkResponse({ type: FeatureFlagDto })
  create(@Body() data: CreateFeatureFlagDto): Promise<FeatureFlagDto> {
    return this.featureFlagService.create(data);
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Put('/:id')
  @ApiOkResponse({ type: FeatureFlagDto })
  update(
    @Param('id') id: string,
    @Body() data: UpdateFeatureFlagDto,
  ): Promise<FeatureFlagDto> {
    return this.featureFlagService.update(id, data);
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Delete('/:id')
  @ApiOkResponse({ type: FeatureFlagDto })
  delete(@Param('id') id: string): Promise<FeatureFlagDto> {
    return this.featureFlagService.delete(id);
  }
}

import { Controller, Get, NotFoundException, Param } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { FeatureFlagService } from '../feature-flag.service';
import { FeatureFlagDto } from '../dtos/feature-flag.dto';

@ApiTags('Feature Flag')
@Controller('/feature-flags')
export class FeatureFlagController {
  constructor(private readonly featureFlagService: FeatureFlagService) {}

  @Get('/')
  @ApiOkResponse({ type: [FeatureFlagDto] })
  async getAll(): Promise<FeatureFlagDto[]> {
    return this.featureFlagService.findAllActive();
  }

  @Get('/:key')
  @ApiOkResponse({ type: FeatureFlagDto })
  async getByKey(@Param('key') key: string): Promise<FeatureFlagDto> {
    const featureFlag = await this.featureFlagService.findByKey(key);

    if (!featureFlag || !featureFlag.isActive) {
      throw new NotFoundException(
        `Feature flag with key ${key} not found or not active`,
      );
    }

    return featureFlag;
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  FacebookAccount,
  FacebookAccountSchema,
  SystemUser,
  SystemUserSchema,
} from '@common/schemas';
import { FacebookManagerController } from '@module/facebook-manager/facebook-manager.controller';
import { FacebookManagerService } from '@module/facebook-manager/facebook-manager.service';
import { LoggerModule } from '@common/logger/logger.module';
import { PermissionsGuard } from '@common/guards';
import { JwtModule } from '@nestjs/jwt';
import { AdminGuard } from '@common/guards/admin.guard';
import { SchemaCollectionName } from '@common/constants/schema';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: FacebookAccount.name,
        schema: FacebookAccountSchema,
        collection: SchemaCollectionName.FacebookAccount,
      },
    ]),
  ],
  controllers: [FacebookManagerController],
  providers: [FacebookManagerService, AdminGuard, PermissionsGuard],
})
export class FacebookManagerModule {}

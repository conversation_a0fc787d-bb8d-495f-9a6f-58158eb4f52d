import { AppLogger } from '@common/logger/logger.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, RootFilterQuery } from 'mongoose';
import { FacebookAccount, FacebookAccountStatus } from '@common/schemas';
import { UpsertFacebookAccountDto } from '@module/facebook-manager/dtos/upsert-facebook-account.dto';
import { FacebookUtil } from '@module/facebook-manager/facebook.util';
import { v4 as uuid } from 'uuid';

@Injectable()
export class FacebookManagerService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(FacebookAccount.name)
    private facebookAccountModel: Model<FacebookAccount>,
  ) {
    this.logger.setContext(FacebookManagerService.name);
  }

  async getAll({ status }): Promise<FacebookAccount[]> {
    const filter: RootFilterQuery<FacebookAccount> = {};
    if (status) {
      filter.status = status;
    }

    return this.facebookAccountModel.find(filter);
  }

  async getOne(id: string): Promise<FacebookAccount> {
    return this.facebookAccountModel.findOne({
      id: id,
    });
  }

  async create(body: UpsertFacebookAccountDto): Promise<FacebookAccount> {
    const { id } = (await FacebookUtil.getProfileByCookie(body.cookies)) || {};

    if (!id) {
      this.logger.error(`Can't get profile id by cookies`);
      throw new NotFoundException(`Can't get profile id by cookies`);
    }

    const response: FacebookAccount = {
      id: uuid(),
      cookies: body.cookies,
      fid: id,
      fullName: body.fullName,
      status: FacebookAccountStatus.ACTIVE,
      avatar: '',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return this.facebookAccountModel.create({
      ...response,
    });
  }

  delete(id: string): Promise<FacebookAccount> {
    return this.facebookAccountModel.findOneAndDelete({
      id: id,
    });
  }

  async update(
    id: string,
    body: UpsertFacebookAccountDto,
  ): Promise<FacebookAccount> {
    const { id: fid } =
      (await FacebookUtil.getProfileByCookie(body.cookies)) || {};

    const facebookAccount = await this.facebookAccountModel.findOne({
      id: id,
    });

    if (!fid && !facebookAccount) {
      this.logger.error(`Can't get profile id by cookies`);
      throw new NotFoundException(`Can't get profile id by cookies`);
    }

    return this.facebookAccountModel.findOneAndUpdate(
      {
        id: id,
      },
      {
        cookies: body.cookies,
        status: fid
          ? FacebookAccountStatus.ACTIVE
          : FacebookAccountStatus.INACTIVE,
        fid: fid,
        fullName: body.fullName,
        updatedAt: new Date(),
      },
      {
        new: true,
      },
    );
  }

  async check(id: string): Promise<FacebookAccount> {
    const facebookAccount = await this.facebookAccountModel.findOne({
      id: id,
    });

    const { id: fid } =
      (await FacebookUtil.getProfileByCookie(facebookAccount.cookies)) || {};

    return this.facebookAccountModel.findOneAndUpdate(
      {
        id: id,
      },
      {
        status: fid
          ? FacebookAccountStatus.ACTIVE
          : FacebookAccountStatus.INACTIVE,
        updatedAt: new Date(),
      },
      {
        new: true,
      },
    );
  }
}

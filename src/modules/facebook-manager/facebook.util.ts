import axios from 'axios';
import { parseCookiesToString } from '@common/utils/cookies';

export class FacebookUtil {
  static async getProfileByCookie(cookies: any[]): Promise<{
    id: string;
  } | null> {
    try {
      const userRegex = new RegExp('"userID":"([0-9]+)"', 'g');
      const pageRegex = new RegExp(
        '"profile_type_name_for_content":"PAGE","id":"([0-9]+)"',
        'g',
      );

      const { data } = await axios.get('https://facebook.com/me', {
        headers: {
          accept:
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
          'cache-control': 'max-age=0',
          dpr: '2',
          priority: 'u=0, i',
          'sec-ch-prefers-color-scheme': 'light',
          'sec-ch-ua':
            '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
          'sec-ch-ua-full-version-list':
            '"Not(A:Brand";v="99.0.0.0", "Google Chrome";v="133.0.6943.98", "Chromium";v="133.0.6943.98"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-model': '""',
          'sec-ch-ua-platform': '"macOS"',
          'sec-ch-ua-platform-version': '"15.3.0"',
          'sec-fetch-dest': 'document',
          'sec-fetch-mode': 'navigate',
          'sec-fetch-site': 'same-origin',
          'sec-fetch-user': '?1',
          'upgrade-insecure-requests': '1',
          'viewport-width': '1512',
          cookie: parseCookiesToString(cookies),
        },
      });

      const match = userRegex.exec(data);

      if (match) {
        return {
          id: match[1],
        };
      }

      const pageMatch = pageRegex.exec(data);

      if (pageMatch) {
        return {
          id: pageMatch[1],
        };
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}

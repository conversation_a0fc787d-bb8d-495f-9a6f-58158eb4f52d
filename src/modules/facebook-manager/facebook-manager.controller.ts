import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { FacebookManagerService } from '@module/facebook-manager/facebook-manager.service';
import { FacebookAccount, FacebookAccountStatus } from '@common/schemas';
import { UpsertFacebookAccountDto } from '@module/facebook-manager/dtos/upsert-facebook-account.dto';
import { AdminGuard } from '@common/guards/admin.guard';

@ApiTags('FacebookManager')
@UseGuards(AdminGuard)
@Controller('/facebook-manager')
export class FacebookManagerController {
  constructor(
    private readonly facebookManagerService: FacebookManagerService,
  ) {}

  @Get('/accounts')
  async getAll(
    @Query('status') status: FacebookAccountStatus,
  ): Promise<FacebookAccount[]> {
    return this.facebookManagerService.getAll({
      status,
    });
  }

  @Get('/accounts/:id')
  async getOne(@Param('id') id: string): Promise<FacebookAccount> {
    return this.facebookManagerService.getOne(id);
  }

  @Post('/accounts')
  async create(
    @Body() body: UpsertFacebookAccountDto,
  ): Promise<FacebookAccount> {
    return this.facebookManagerService.create(body);
  }

  @Put('/accounts/:id')
  async update(
    @Param('id') id: string,
    @Body() body: UpsertFacebookAccountDto,
  ): Promise<FacebookAccount> {
    return this.facebookManagerService.update(id, body);
  }

  @Post('/accounts/:id/check')
  async check(@Param('id') id: string): Promise<FacebookAccount> {
    return this.facebookManagerService.check(id);
  }

  @Delete('/accounts/:id')
  async delete(@Param('id') id: string): Promise<FacebookAccount> {
    return this.facebookManagerService.delete(id);
  }
}

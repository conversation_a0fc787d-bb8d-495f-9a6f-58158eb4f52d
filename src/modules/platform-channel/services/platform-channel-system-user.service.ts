import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelDocument,
} from '@common/schemas/system-user-platform-channel.schema';
import { DeleteResult } from 'mongodb';
import { SystemConfigService } from '@module/system-config/system-config.service';
import { LivestreamPlatform } from '@common/constants';
import {
  PlatformChannel,
  PlatformChannelDocument,
} from '@common/schemas/platform-channel.schema';
import { Client } from 'youtubei';

@Injectable()
export class PlatformChannelSystemUserService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(SystemUserPlatformChannel.name)
    private readonly systemUserPlatformChannelModel: Model<SystemUserPlatformChannelDocument>,
    @InjectModel(PlatformChannel.name)
    private readonly platformChannelModel: Model<PlatformChannelDocument>,
    private readonly systemConfigService: SystemConfigService,
  ) {
    this.logger.setContext(PlatformChannelSystemUserService.name);
  }

  create(userId: string, platformChannelId: string) {
    return this.systemUserPlatformChannelModel.create({
      systemUser: userId,
      platformChannel: platformChannelId,
    });
  }

  async canAccess(userId: string, platformChannelId: string): Promise<boolean> {
    const strictChannel = await this.systemConfigService.strictChannel();

    if (!strictChannel) return true;

    const data = await this.systemUserPlatformChannelModel
      .findOne({ systemUser: userId, platformChannel: platformChannelId })
      .exec();

    return !!data;
  }

  async canAccessByUserAndLiveId(
    userId: string,
    liveId: string,
    platform: LivestreamPlatform,
  ): Promise<boolean> {
    if (platform === LivestreamPlatform.FACEBOOK) return true;

    const strictChannel = await this.systemConfigService.strictChannel();

    if (!strictChannel) return true;

    switch (platform) {
      case LivestreamPlatform.TIKTOK: {
        const data = await this.platformChannelModel
          .findOne({
            channelId: liveId,
            livestreamPlatform: LivestreamPlatform.TIKTOK,
          })
          .exec();

        if (!data) {
          this.logger.error(
            `Platform channel not found for user ${userId} and liveId ${liveId}`,
          );
          return false;
        }

        const assignedChannel = await this.systemUserPlatformChannelModel
          .findOne({ systemUser: userId, platformChannel: data.id })
          .exec();

        return !!assignedChannel;
      }
      case LivestreamPlatform.YOUTUBE: {
        const youtubeClient = new Client();
        const video = await youtubeClient.getVideo(liveId);
        const channelId = video.channel.id;

        const data = await this.platformChannelModel
          .findOne({
            channelId,
            livestreamPlatform: LivestreamPlatform.YOUTUBE,
          })
          .exec();

        if (!data) {
          this.logger.error(
            `Platform channel not found for user ${userId} and liveId ${liveId}`,
          );
          return false;
        }

        const assignedChannel = await this.systemUserPlatformChannelModel
          .findOne({ systemUser: userId, platformChannel: data.id })
          .exec();

        return !!assignedChannel;
      }
      default:
        throw new Error('Platform not supported');
    }
  }

  findUserIdsByPlatformChannelId(platformChannelId: string) {
    return this.systemUserPlatformChannelModel
      .find({ platformChannel: platformChannelId })
      .distinct('systemUser')
      .exec();
  }

  findPlatformChannelIdsByUserId(userId: string) {
    return this.systemUserPlatformChannelModel
      .find({ systemUser: userId })
      .distinct('platformChannel')
      .exec();
  }

  removeByPlatformChannelId(platformChannelId: string): Promise<DeleteResult> {
    return this.systemUserPlatformChannelModel
      .deleteMany({ platformChannel: platformChannelId })
      .exec();
  }
}

import { AppLogger } from '@common/logger/logger.service';
import {
  ConflictException,
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PaginationService } from '@shared/services/pagination.service';
import {
  PlatformChannel,
  PlatformChannelDocument,
} from '@common/schemas/platform-channel.schema';
import { SubscriptionPrivilegeKey } from '@common/schemas';
import { FilterQuery, Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { UpsertPlatformChannelDto } from '@module/platform-channel/dtos/upsert-platform-channel.dto';
import { PlatformChannelPaginationResultDto } from '@module/platform-channel/dtos/platform-channel-pagination.dto';
import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';
import { LivestreamPlatform } from '@common/constants';
import { PlatformChannelSystemUserService } from '@module/platform-channel/services/platform-channel-system-user.service';
import { Client } from 'youtubei';
import { PlatformChannelWithRelationsDto } from '@module/platform-channel/dtos/platform-channel-with-relations.dto';
import { GetPlatformChannelDto } from '@module/platform-channel/dtos/get-platform-channel.dto';
import { uniq } from 'lodash';
import { AppException } from '@common/exceptions/app-exception';
import { PLATFORM_CHANNEL_NOT_EXIST } from '@common/exceptions/error';
import { FacebookClient } from '@infras/platforms/facebook-client';
import { UserSubscriptionService } from '@module/subscription/user-subscription.service';
import {
  SystemUser,
  SystemUserDocument,
  SystemUserRole,
} from '@common/schemas';
import { UserService } from '@module/user/user.service';
import { CreatorDto } from '@module/user/dtos/creator.dto';

@Injectable()
export class PlatformChannelService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(PlatformChannel.name)
    private readonly platformChannelModel: Model<PlatformChannelDocument>,
    @InjectModel(SystemUser.name)
    private readonly systemUserModel: Model<SystemUserDocument>,
    private readonly paginationService: PaginationService,
    private readonly platformChannelSystemUserService: PlatformChannelSystemUserService,
    private readonly userService: UserService,
    private readonly userSubscriptionService: UserSubscriptionService,
  ) {
    this.logger.setContext(PlatformChannelService.name);
  }

  async get(id: string): Promise<PlatformChannelWithRelationsDto> {
    const platformChannel = await this.platformChannelModel.findOne({ id });
    if (!platformChannel) {
      throw new NotFoundException(`Platform channel with ID ${id} not found`);
    }

    const userIds =
      await this.platformChannelSystemUserService.findUserIdsByPlatformChannelId(
        id,
      );

    const creator = await this.getCreatorForPlatformChannel(
      platformChannel.createdBy,
    );

    return this.toDtoWithRelations(platformChannel, userIds, creator);
  }

  async findMyPlatformChannelsPaginate(
    userId: string,
    options: PaginationOptionsDto,
    filters: GetPlatformChannelDto,
  ): Promise<PlatformChannelPaginationResultDto> {
    const platformChannelIds =
      await this.platformChannelSystemUserService.findPlatformChannelIdsByUserId(
        userId,
      );

    const filterQuery: FilterQuery<PlatformChannelDocument> = {
      id: { $in: platformChannelIds },
    };

    if (filters.livestreamPlatform) {
      filterQuery.livestreamPlatform = filters.livestreamPlatform;
    }

    if (filters.search) {
      filterQuery.$or = [
        { name: { $regex: filters.search, $options: 'i' } },
        { channelId: { $regex: filters.search, $options: 'i' } },
      ];
    }

    const result = await this.paginationService.paginate(
      this.platformChannelModel,
      filterQuery,
      options,
    );

    // Fetch creators for all platform channels in a single query
    const creatorsMap = await this.getCreatorsForPlatformChannels(result.data);

    // Map platform channels to DTOs with their creators
    const dtos = result.data.map(platformChannel => {
      const creator = platformChannel.createdBy
        ? creatorsMap.get(platformChannel.createdBy)
        : null;
      return this.toDto(platformChannel, creator);
    });

    return {
      data: dtos,
      meta: result.meta,
    };
  }

  async paginate(
    options: PaginationOptionsDto,
  ): Promise<PlatformChannelPaginationResultDto> {
    const result = await this.paginationService.paginate(
      this.platformChannelModel,
      {},
      options,
    );

    // Fetch creators for all platform channels in a single query
    const creatorsMap = await this.getCreatorsForPlatformChannels(result.data);

    // Map platform channels to DTOs with their creators
    const dtos = result.data.map(platformChannel => {
      const creator = platformChannel.createdBy
        ? creatorsMap.get(platformChannel.createdBy)
        : null;
      return this.toDto(platformChannel, creator);
    });

    return {
      data: dtos,
      meta: result.meta,
    };
  }

  async create(
    data: UpsertPlatformChannelDto,
    createdBy: string,
    options = { bypassCheckingLimit: false },
  ): Promise<PlatformChannelDto> {
    if (!options.bypassCheckingLimit) {
      // Check if user has reached their platform channel limit
      await this.validateUserPlatformChannelLimit(createdBy);
    }

    const channelId = await this.convertRecognizeIdToChannelId(
      data.recognizeId,
      data.livestreamPlatform,
    );

    if (!channelId) {
      throw new NotFoundException('Cannot find channel ID');
    }

    const exists = await this.platformChannelModel.findOne({
      channelId,
      livestreamPlatform: data.livestreamPlatform,
      createdBy,
    });

    if (exists) {
      throw new ConflictException('Platform channel already exists');
    }

    const platformChannel = await this.platformChannelModel.create({
      channelId,
      livestreamPlatform: data.livestreamPlatform,
      recognizeId: data.recognizeId,
      name: data.name,
      createdBy,
    });

    if ((data.userIds || []).length) {
      await Promise.all(
        uniq(data.userIds).map(userId =>
          this.platformChannelSystemUserService.create(
            userId,
            platformChannel.id,
          ),
        ),
      );
    }

    // Get creator information
    const creator = await this.getCreatorForPlatformChannel(createdBy);

    return this.toDto(platformChannel, creator);
  }

  async update(
    id: string,
    data: UpsertPlatformChannelDto,
    createdBy: string,
  ): Promise<PlatformChannelDto> {
    const channelId = await this.convertRecognizeIdToChannelId(
      data.recognizeId,
      data.livestreamPlatform,
    );

    if (!channelId) {
      throw new NotFoundException('Cannot find channel ID');
    }

    const platformChannel = await this.platformChannelModel.findOneAndUpdate(
      { id },
      {
        channelId,
        livestreamPlatform: data.livestreamPlatform,
        recognizeId: data.recognizeId,
        name: data.name,
        // createdBy,
        updatedAt: new Date(),
      },
      { new: true },
    );

    if ((data.userIds || []).length) {
      await this.platformChannelSystemUserService.removeByPlatformChannelId(id);
      await Promise.all(
        uniq(data.userIds).map(userId =>
          this.platformChannelSystemUserService.create(
            userId,
            platformChannel.id,
          ),
        ),
      );
    }

    // Get creator information
    const creator = await this.getCreatorForPlatformChannel(createdBy);

    return this.toDto(platformChannel, creator);
  }

  async delete(id: string, deletedBy: string): Promise<PlatformChannelDto> {
    const platformChannel = await this.platformChannelModel.findOneAndDelete({
      id,
    });

    if (!platformChannel) {
      throw new NotFoundException(PLATFORM_CHANNEL_NOT_EXIST);
    }

    const userActionDeleted = await this.systemUserModel.findOne({
      id: deletedBy,
    });

    if (
      platformChannel.createdBy !== userActionDeleted.id &&
      userActionDeleted.role !== SystemUserRole.ADMIN
    ) {
      throw new NotFoundException(PLATFORM_CHANNEL_NOT_EXIST);
    }

    await this.platformChannelSystemUserService.removeByPlatformChannelId(id);

    // Get creator information - we already have the creator from the userActionDeleted
    const creatorDto = this.userService.toCreatorDto(userActionDeleted);
    return this.toDto(platformChannel, creatorDto);
  }

  async convertRecognizeIdToChannelId(
    recognizeId: string,
    platform: LivestreamPlatform,
  ): Promise<string> {
    switch (platform) {
      case LivestreamPlatform.YOUTUBE:
        const youtube = new Client();
        recognizeId = recognizeId.startsWith('@')
          ? recognizeId.slice(1)
          : recognizeId;
        const channel = await youtube.findOne(recognizeId, { type: 'channel' });

        return channel?.id;
      default:
        return recognizeId;
    }
  }

  canAccess(userId: string, platformChannelId: string): Promise<boolean> {
    return this.platformChannelSystemUserService.canAccess(
      userId,
      platformChannelId,
    );
  }

  canAccessByUserAndLiveId(
    userId: string,
    liveId: string,
    platform: LivestreamPlatform,
  ): Promise<boolean> {
    return this.platformChannelSystemUserService.canAccessByUserAndLiveId(
      userId,
      liveId,
      platform,
    );
  }

  /**
   * Validate if a user has reached their platform channel limit based on their subscription
   * @param userId The user ID to check
   * @throws ForbiddenException if the user has reached their limit
   */
  private async validateUserPlatformChannelLimit(
    userId: string,
  ): Promise<void> {
    try {
      // Get the user's current platform channels count
      const platformChannelIds =
        await this.platformChannelSystemUserService.findPlatformChannelIdsByUserId(
          userId,
        );
      const currentCount = platformChannelIds.length;

      // Check if the user has reached their limit
      const hasReachedLimit =
        await this.userSubscriptionService.hasUserReachedPrivilegeLimit(
          userId,
          SubscriptionPrivilegeKey.PLATFORM_CHANNELS,
          currentCount,
        );

      if (hasReachedLimit) {
        // Get the user's subscription to provide a helpful error message
        const subscription =
          await this.userSubscriptionService.getUserSubscription(userId);
        const privilege = subscription
          ? subscription.privileges.find(
              p => p.key === SubscriptionPrivilegeKey.PLATFORM_CHANNELS,
            )
          : null;

        const limit = privilege?.limit || 0;
        const planName = subscription?.name || 'Free';

        throw new ForbiddenException(
          `You have reached your platform channel limit (${currentCount}/${limit}) with your ${planName} subscription. ` +
            `Please upgrade your subscription to create more platform channels.`,
        );
      }
    } catch (error) {
      // If the error is already a ForbiddenException, rethrow it
      if (error instanceof ForbiddenException) {
        throw error;
      }

      // Otherwise, log the error and throw a generic error
      this.logger.error(
        `Error validating platform channel limit: ${error.message}`,
        error.stack,
      );
      throw new ForbiddenException(
        'Unable to validate subscription privileges. Please try again later.',
      );
    }
  }

  async getRoomIdById(id: string): Promise<string> {
    const platformChannel = await this.platformChannelModel.findOne({
      id,
    });

    if (!platformChannel) {
      throw new AppException(PLATFORM_CHANNEL_NOT_EXIST);
    }

    switch (platformChannel.livestreamPlatform) {
      case LivestreamPlatform.YOUTUBE:
        const youtube = new Client();
        const channel = await youtube.getChannel(platformChannel.channelId);
        const lives = await channel.live.next();
        const currentLive = lives.find(live => live.isLive);

        return currentLive?.id || '';
      case LivestreamPlatform.TIKTOK:
        return platformChannel.channelId;
      case LivestreamPlatform.FACEBOOK:
        const facebook = new FacebookClient();
        const roomId = await facebook.getCurrentLiveIdByPageUrl(
          platformChannel.channelId,
        );

        return roomId || '';
      default:
        return platformChannel.channelId;
    }
  }

  /**
   * Fetches creator information for a platform channel
   */
  private async getCreatorForPlatformChannel(
    createdById: string,
  ): Promise<CreatorDto | null> {
    if (!createdById) return null;
    const user = await this.systemUserModel.findOne({ id: createdById });
    return this.userService.toCreatorDto(user);
  }

  /**
   * Fetches creators for multiple platform channels
   */
  private async getCreatorsForPlatformChannels(
    platformChannels: PlatformChannelDocument[],
  ): Promise<Map<string, CreatorDto>> {
    // Extract unique creator IDs
    const creatorIds = [
      ...new Set(
        platformChannels
          .filter(channel => channel.createdBy)
          .map(channel => channel.createdBy),
      ),
    ];

    if (creatorIds.length === 0) return new Map();

    // Use the UserService to fetch creators
    return await this.userService.getCreatorsByIds(creatorIds);
  }

  /**
   * Transforms a platform channel document to a DTO with creator information
   */
  toDto(
    platformChannel: PlatformChannelDocument,
    creator?: CreatorDto,
  ): PlatformChannelDto {
    return new PlatformChannelDto({
      id: platformChannel.id,
      channelId: platformChannel.channelId,
      recognizeId: platformChannel.recognizeId,
      livestreamPlatform: platformChannel.livestreamPlatform,
      name: platformChannel.name,
      createdBy: creator || null,
    });
  }

  /**
   * Transforms a platform channel document to a DTO with relations
   */
  toDtoWithRelations(
    platformChannel: PlatformChannelDocument,
    userIds: string[] = [],
    creator?: CreatorDto,
  ): PlatformChannelWithRelationsDto {
    const baseDto = this.toDto(platformChannel, creator);

    return new PlatformChannelWithRelationsDto({
      ...baseDto,
      userIds,
    });
  }
}

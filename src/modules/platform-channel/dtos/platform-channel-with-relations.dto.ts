import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class PlatformChannelWithRelationsDto extends PlatformChannelDto {
  @ApiPropertyOptional({
    type: [String],
    isArray: true,
    default: ['60b8e3c7-0f1a-4c2f-8d3b-2d9f0d8b4f5a'],
  })
  @Expose()
  userIds?: string[];

  constructor(partial: Partial<PlatformChannelWithRelationsDto>) {
    super(partial);
    Object.assign(this, partial);
  }
}

import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { LivestreamPlatform } from '@common/constants';
import { CreatorDto } from '@module/user/dtos/creator.dto';

export class PlatformChannelDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  channelId: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  recognizeId: string;

  @ApiProperty({ enum: LivestreamPlatform })
  @Expose()
  livestreamPlatform: LivestreamPlatform;

  @ApiProperty({ type: CreatorDto })
  @Expose()
  createdBy: CreatorDto;

  constructor(partial: Partial<PlatformChannelDto>) {
    Object.assign(this, partial);
  }
}

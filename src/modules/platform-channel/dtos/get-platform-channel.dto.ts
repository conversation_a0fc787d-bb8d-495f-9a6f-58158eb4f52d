import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { LivestreamPlatform } from '@common/constants';

export class GetPlatformChannelDto {
  @ApiPropertyOptional({ type: String, nullable: true })
  @IsOptional()
  @IsString()
  search: string;

  @ApiPropertyOptional({ enum: LivestreamPlatform, nullable: true })
  @IsEnum(LivestreamPlatform)
  @IsOptional()
  livestreamPlatform: LivestreamPlatform;
}

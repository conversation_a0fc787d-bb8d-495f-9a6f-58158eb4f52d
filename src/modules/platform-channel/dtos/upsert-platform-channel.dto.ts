import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LivestreamPlatform } from '@common/constants';

export class UpsertPlatformChannelDto {
  @ApiProperty()
  @IsString()
  recognizeId: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: LivestreamPlatform })
  @IsEnum(LivestreamPlatform)
  livestreamPlatform: LivestreamPlatform;

  @ApiPropertyOptional({
    type: [String],
    default: [
      '60b8e3c7-0f1a-4c2f-8d3b-2d9f0d8b4f5a',
      '60b8e3c7-0f1a-4c2f-8d3b-2d9f0d8b4f5b',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  userIds?: string[];
}

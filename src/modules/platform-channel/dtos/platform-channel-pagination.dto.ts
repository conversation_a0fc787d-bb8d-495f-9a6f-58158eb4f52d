import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';

export class PlatformChannelPaginationResultDto extends PaginationResultDto<PlatformChannelDto> {
  @ApiProperty({ type: [PlatformChannelDto] })
  data: PlatformChannelDto[];

  @ApiProperty({ type: PaginationMetaDto })
  meta: PaginationMetaDto;
}

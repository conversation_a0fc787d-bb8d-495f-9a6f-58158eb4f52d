import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  PlatformChannel,
  PlatformChannelSchema,
} from '@common/schemas/platform-channel.schema';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelSchema,
} from '@common/schemas/system-user-platform-channel.schema';
import { SchemaCollectionName } from '@common/constants/schema';
import { SharedModule } from '@shared/shared.module';
import { PlatformChannelService } from '@module/platform-channel/services/platform-channel.service';
import { PlatformChannelController } from '@module/platform-channel/controllers/platform-channel.controller';
import { AdminPlatformChannelController } from '@module/platform-channel/controllers/admin-platform-channel.controller';
import { PlatformChannelSystemUserService } from '@module/platform-channel/services/platform-channel-system-user.service';
import { AdminGuard } from '@common/guards/admin.guard';
import { JwtModule } from '@nestjs/jwt';
import { SystemUser, SystemUserSchema } from '@common/schemas';
import { SubscriptionModule } from '@module/subscription/subscription.module';
import { SystemConfigModule } from '@module/system-config/system-config.module';
import { UserModule } from '@module/user/user.module';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    SharedModule,
    SystemConfigModule,
    UserModule,
    SubscriptionModule,
    MongooseModule.forFeature([
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
      {
        name: PlatformChannel.name,
        schema: PlatformChannelSchema,
        collection: SchemaCollectionName.PlatformChannel,
      },
      {
        name: SystemUserPlatformChannel.name,
        schema: SystemUserPlatformChannelSchema,
        collection: SchemaCollectionName.SystemUserPlatformChannel,
      },
    ]),
  ],
  controllers: [PlatformChannelController, AdminPlatformChannelController],
  providers: [
    PlatformChannelService,
    PlatformChannelSystemUserService,
    AdminGuard,
  ],
  exports: [
    PlatformChannelService,
    PlatformChannelSystemUserService,
    AdminGuard,
  ],
})
export class PlatformChannelModule {}

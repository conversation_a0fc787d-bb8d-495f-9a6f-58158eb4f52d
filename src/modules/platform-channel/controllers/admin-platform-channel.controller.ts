import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { PlatformChannelService } from '@module/platform-channel/services/platform-channel.service';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { PlatformChannelPaginationResultDto } from '@module/platform-channel/dtos/platform-channel-pagination.dto';
import { UpsertPlatformChannelDto } from '@module/platform-channel/dtos/upsert-platform-channel.dto';
import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';
import { AdminGuard } from '@common/guards/admin.guard';
import { PlatformChannelWithRelationsDto } from '@module/platform-channel/dtos/platform-channel-with-relations.dto';
import { Auth, CurrentUser } from '@common/decorators';
import { UserPayload } from '@module/user/types';

@ApiTags('Platform Channel management for Admin')
@Controller('/admin/platform-channels')
export class AdminPlatformChannelController {
  constructor(
    private readonly platformChannelService: PlatformChannelService,
  ) {}

  @UseGuards(AdminGuard)
  @Auth()
  @Get('/')
  @ApiOkResponse({ type: PlatformChannelPaginationResultDto })
  paginate(
    @Query() paginationOptions: PaginationOptionsDto,
  ): Promise<PlatformChannelPaginationResultDto> {
    return this.platformChannelService.paginate(paginationOptions);
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Get('/:id')
  @ApiOkResponse({ type: PlatformChannelWithRelationsDto })
  get(@Param('id') id: string): Promise<PlatformChannelWithRelationsDto> {
    return this.platformChannelService.get(id);
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Post('/')
  @ApiOkResponse({ type: PlatformChannelDto })
  create(
    @Body() data: UpsertPlatformChannelDto,
    @CurrentUser() user: UserPayload,
  ): Promise<PlatformChannelDto> {
    return this.platformChannelService.create(data, user.id, {
      bypassCheckingLimit: true,
    });
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Put('/:id')
  @ApiOkResponse({ type: PlatformChannelDto })
  update(
    @Param('id') id: string,
    @Body() data: UpsertPlatformChannelDto,
    @CurrentUser() user: UserPayload,
  ): Promise<PlatformChannelDto> {
    return this.platformChannelService.update(id, data, user.id);
  }

  @UseGuards(AdminGuard)
  @Auth()
  @Delete('/:id')
  @ApiOkResponse({ type: PlatformChannelDto })
  delete(
    @Param('id') id: string,
    @CurrentUser() user: UserPayload,
  ): Promise<PlatformChannelDto> {
    return this.platformChannelService.delete(id, user.id);
  }
}

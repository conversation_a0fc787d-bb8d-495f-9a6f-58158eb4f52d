import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { PlatformChannelService } from '@module/platform-channel/services/platform-channel.service';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { PlatformChannelPaginationResultDto } from '@module/platform-channel/dtos/platform-channel-pagination.dto';
import { GetPlatformChannelDto } from '@module/platform-channel/dtos/get-platform-channel.dto';
import { Auth, CurrentUser } from '@common/decorators';
import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';
import { UpsertPlatformChannelDto } from '@module/platform-channel/dtos/upsert-platform-channel.dto';
import { UserPayload } from '@module/user/types';

@ApiTags('PlatformChannel')
@Controller('/platform-channels')
export class PlatformChannelController {
  constructor(
    private readonly platformChannelService: PlatformChannelService,
  ) {}

  @Auth()
  @Get('/')
  @ApiOkResponse({ type: PlatformChannelPaginationResultDto })
  myPlatformChannels(
    @Query() paginationOptions: PaginationOptionsDto,
    @Query() filters: GetPlatformChannelDto,
    @CurrentUser() user: UserPayload,
  ) {
    return this.platformChannelService.findMyPlatformChannelsPaginate(
      user.id,
      paginationOptions,
      filters,
    );
  }

  @Auth()
  @Post('/')
  @ApiOkResponse({ type: PlatformChannelDto })
  create(
    @Body() data: UpsertPlatformChannelDto,
    @CurrentUser() user: UserPayload,
  ): Promise<PlatformChannelDto> {
    data.userIds = [user.id];
    return this.platformChannelService.create(data, user.id, {
      bypassCheckingLimit: true,
    });
  }

  @Auth()
  @Put('/:id')
  @ApiOkResponse({ type: PlatformChannelDto })
  update(
    @Param('id') id: string,
    @Body() data: UpsertPlatformChannelDto,
    @CurrentUser() user: UserPayload,
  ): Promise<PlatformChannelDto> {
    data.userIds = [user.id];
    return this.platformChannelService.update(id, data, user.id);
  }

  @Auth()
  @Delete('/:id')
  @ApiOkResponse({ type: PlatformChannelDto })
  delete(
    @Param('id') id: string,
    @CurrentUser() user: UserPayload,
  ): Promise<PlatformChannelDto> {
    return this.platformChannelService.delete(id, user.id);
  }
}

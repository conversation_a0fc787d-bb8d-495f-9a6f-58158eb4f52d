import { BaseStreamHubWatcher } from '@module/stream-hub/watchers/providers/base-stream-hub.watcher';
import {
  StreamHubOutput,
  StreamHubStorageProvider,
} from '@module/stream-hub/types';
import { RedisService } from '@shared/redis';
import { Server } from 'socket.io';
import { AppLogger } from '@common/logger/logger.service';
import { createAdapter } from '@socket.io/redis-adapter';
import { Injectable } from '@nestjs/common';
import { StreamHubEvent } from '@module/stream-hub/constants';
import * as dayjs from 'dayjs';

const STREAM_HUB_REDIS_PREFIX = 'stream-hub';

@Injectable()
export class StreamHubRedisWatcher extends BaseStreamHubWatcher {
  constructor(
    protected readonly logger: AppLogger,
    protected readonly redisService: RedisService,
  ) {
    super();

    logger.setContext(StreamHubRedisWatcher.name);
  }

  getName(): StreamHubStorageProvider {
    return 'redis';
  }

  async store(data: StreamHubOutput) {
    await this.redisService.set(
      `${STREAM_HUB_REDIS_PREFIX}:user:${data.userId}:socket:${data.socketId}:channel:${data.platformChannelId}:session:${data.sessionId}`,
      JSON.stringify(data),
    );
  }

  async getBySocketId(socketId: string): Promise<StreamHubOutput[]> {
    const keyPattern = `${STREAM_HUB_REDIS_PREFIX}:user:*:socket:${socketId}:*`;

    const keys = await this.redisService.keys(keyPattern);

    if (keys.length === 0) return [];

    return Promise.all(keys.map(key => this.redisService.get(key))).then(
      (results: string[]) =>
        results.filter(Boolean).map(item => JSON.parse(item)),
    );
  }

  async getByUserId(userId: string): Promise<StreamHubOutput[]> {
    const keyPattern = `${STREAM_HUB_REDIS_PREFIX}:user:${userId}:*`;

    const keys = await this.redisService.keys(keyPattern);

    if (keys.length === 0) return [];

    return Promise.all(keys.map(key => this.redisService.get(key))).then(
      (results: string[]) =>
        results.filter(Boolean).map(item => JSON.parse(item)),
    );
  }

  async getByPlatformChannelId(
    platformChannelId: string,
    options?: { userId?: string },
  ): Promise<StreamHubOutput[]> {
    const keyPattern = `${STREAM_HUB_REDIS_PREFIX}:user:${
      options?.userId || '*'
    }:socket:*:channel:${platformChannelId}:session:*`;

    const keys = await this.redisService.keys(keyPattern);

    if (keys.length === 0) return [];

    return Promise.all(keys.map(key => this.redisService.get(key))).then(
      (results: string[]) =>
        results.filter(Boolean).map(item => JSON.parse(item)),
    );
  }

  async getBySessionId(sessionId: string): Promise<StreamHubOutput | null> {
    const keyPattern = `${STREAM_HUB_REDIS_PREFIX}:user:*:socket:*:channel:*:session:${sessionId}`;

    const keys = await this.redisService.keys(keyPattern);

    if (keys.length === 0) return null;

    const result = await this.redisService.get(keys[0]);

    try {
      return JSON.parse(result);
    } catch (error) {
      this.logger.error('Error parsing result:', error);
      return null;
    }
  }

  async removeBySessionId(sessionId: string): Promise<void> {
    const keyPattern = `${STREAM_HUB_REDIS_PREFIX}:user:*:socket:*:channel:*:session:${sessionId}`;

    const keys = await this.redisService.keys(keyPattern);

    if (keys.length === 0) return;

    await Promise.all(keys.map(key => this.redisService.del(key)));

    return;
  }

  async removeBySocketId(socketId: string): Promise<void> {
    const keyPattern = `${STREAM_HUB_REDIS_PREFIX}:user:*:socket:${socketId}:*`;

    const keys = await this.redisService.keys(keyPattern);

    if (keys.length === 0) return;

    await Promise.all(keys.map(key => this.redisService.del(key)));

    return;
  }

  async removeByPlatformChannelId(
    platformChannelId: string,
    options?: {
      userId?: string;
      socketId?: string;
    },
  ): Promise<void> {
    const keyPattern = `${STREAM_HUB_REDIS_PREFIX}:user:${
      options?.userId || '*'
    }:socket:${options?.socketId || '*'}:channel:${platformChannelId}:*`;

    const keys = await this.redisService.keys(keyPattern);

    if (keys.length === 0) return;

    await Promise.all(keys.map(key => this.redisService.del(key)));

    return;
  }

  applyAdapter(server: Server) {
    this.logger.log('Init server');

    try {
      // Get pub/sub clients from the connection manager
      const pubClient = this.redisService.getPubClient();
      const subClient = this.redisService.getSubClient();

      // Create and set the Redis adapter
      const adapter = createAdapter(pubClient, subClient);
      server.adapter(adapter);

      this.logger.log('Socket.IO server is now using Redis adapter');
    } catch (error) {
      this.logger.error(
        `Failed to create Socket.IO Redis adapter: ${error.message}`,
      );
    }
  }

  triggerSessionStop(sessionId: string) {
    const client = this.redisService.getPubClient();

    client.publish(StreamHubEvent.STOP_SESSION, sessionId);

    this.logger.log(`Published stop session event for: ${sessionId}`);
  }

  triggerPlatformChannelStop(
    platformChannelId: string,
    options?: { socketId?: string },
  ) {
    const client = this.redisService.getPubClient();

    client.publish(
      StreamHubEvent.STOP_PLATFORM_CHANNEL,
      `${platformChannelId}${options?.socketId ? `:${options.socketId}` : ''}`,
    );

    this.logger.log(
      `Published stop platform channel event for: ${platformChannelId}`,
    );
  }

  triggerSocketStop(socketId: string) {
    const client = this.redisService.getPubClient();

    client.publish(StreamHubEvent.STOP_SOCKET, socketId);

    this.logger.log(`Published stop socket event for: ${socketId}`);
  }

  listenStopSession() {
    const client = this.redisService.getSubClient();

    client.subscribe(StreamHubEvent.STOP_SESSION);

    client.on('message', async (channel, sessionId) => {
      if (channel === StreamHubEvent.STOP_SESSION) {
        try {
          await this.removeBySessionId(sessionId);
          this.logger.log(`Received stop session event: ${sessionId}`);
          // Handle the stop session logic here
        } catch (error) {
          this.logger.error('Error handling stop session event:', error);
        }
      }
    });
  }

  listenStopPlatformChannelId() {
    const client = this.redisService.getSubClient();

    client.subscribe(StreamHubEvent.STOP_PLATFORM_CHANNEL);

    client.on('message', async (channel, value) => {
      if (channel === StreamHubEvent.STOP_PLATFORM_CHANNEL) {
        try {
          const [platformChannelId, socketId] = value.split(':');
          await this.removeByPlatformChannelId(platformChannelId, {
            socketId,
          });
          this.logger.log(
            `Received stop platform channel event: ${platformChannelId}`,
          );
          // Handle the stop platform channel logic here
        } catch (error) {
          this.logger.error(
            'Error handling stop platform channel event:',
            error,
          );
        }
      }
    });
  }

  listenStopSocket() {
    const client = this.redisService.getSubClient();

    client.subscribe(StreamHubEvent.STOP_SOCKET);

    client.on('message', async (channel, socketId) => {
      if (channel === StreamHubEvent.STOP_SOCKET) {
        try {
          await this.removeBySocketId(socketId);
          this.logger.log(`Received stop socket event: ${socketId}`);
          // Handle the stop platform channel logic here
        } catch (error) {
          this.logger.error(
            'Error handling stop platform channel event:',
            error,
          );
        }
      }
    });
  }

  async listenCleanup() {
    const client = this.redisService.getSubClient().duplicate();

    client.config('SET', 'notify-keyspace-events', 'AKE');

    client.psubscribe(`__keyevent@0__:expired`, '__keyevent@0__:del');

    client.on('pmessage', async (_, __, key) => {
      const [sessionId] = key.split(':').slice(-1);

      if (sessionId) {
        const disconnect = this.getDisconnectBySessionId(sessionId);

        if (disconnect) {
          disconnect();
        }

        await this.redisService.del(
          `${STREAM_HUB_REDIS_PREFIX}:session:${sessionId}:sent-comments`,
        );
      }
    });
  }

  async addCommentHasBeenSent(
    sessionId: string,
    commentId: string,
  ): Promise<void> {
    await this.redisService.sadd(
      `${STREAM_HUB_REDIS_PREFIX}:session:${sessionId}:sent-comments`,
      commentId,
    );
  }

  async hasCommentBeenSent(
    sessionId: string,
    commentId: string,
  ): Promise<boolean> {
    const result = await this.redisService.sismember(
      `${STREAM_HUB_REDIS_PREFIX}:session:${sessionId}:sent-comments`,
      commentId,
    );
    return result === 1;
  }

  async updateHeartbeat(userId: string) {
    const key = `${STREAM_HUB_REDIS_PREFIX}:heartbeat:${userId}`;
    const value = Date.now().toString();

    await this.redisService.set(key, value, 60 * 5);
  }

  async addDailyUsage(value: number, userId: string) {
    const key = `${STREAM_HUB_REDIS_PREFIX}:daily-usage:${dayjs().format(
      'DD/MM/YYYY',
    )}:${userId}`;
    const currentUsage = await this.redisService.get(key);

    const newUsage = (parseInt(currentUsage, 10) || 0) + value;

    await this.redisService.set(key, newUsage, 60 * 60 * 24); // Set TTL to 24 hours
  }

  async getDailyUsage(userId: string): Promise<number> {
    const key = `${STREAM_HUB_REDIS_PREFIX}:daily-usage:${dayjs().format(
      'DD/MM/YYYY',
    )}:${userId}`;
    const usage = await this.redisService.get(key);
    return parseInt(usage, 10) || 0; // Return 0 if no usage found
  }
}

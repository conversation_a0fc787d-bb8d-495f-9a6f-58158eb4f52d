import {
  StreamHubOutput,
  StreamHubStorageProvider,
} from '@module/stream-hub/types';
import { Server } from 'socket.io';
import { StreamHubMemory } from '@module/stream-hub/memories/stream-hub.memory';

export abstract class BaseStreamHubWatcher {
  abstract getName(): StreamHubStorageProvider;

  abstract applyAdapter(server: Server): void;

  abstract listenStopSocket(): void;

  abstract listenStopSession(): void;

  abstract listenStopPlatformChannelId(): void;

  abstract listenCleanup(): void;

  abstract triggerSocketStop(socketId: string): void;

  abstract triggerPlatformChannelStop(
    platformChannelId: string,
    options?: {
      socketId?: string;
    },
  ): void;

  abstract triggerSessionStop(sessionId: string): void;

  abstract store(data: StreamHubOutput): void;

  abstract getByPlatformChannelId(
    platformChannelId: string,
    options?: {
      userId?: string;
    },
  ): Promise<StreamHubOutput[]>;

  abstract getBySocketId(socketId: string): Promise<StreamHubOutput[]>;

  abstract getByUserId(userId: string): Promise<StreamHubOutput[]>;

  abstract getBySessionId(sessionId: string): Promise<StreamHubOutput | null>;

  abstract removeBySessionId(sessionId: string): Promise<void>;

  abstract removeBySocketId(socketId: string): Promise<void>;

  abstract removeByPlatformChannelId(
    platformChannelId: string,
    options?: {
      userId?: string;
      socketId?: string;
    },
  ): Promise<void>;

  abstract addCommentHasBeenSent(
    sessionId: string,
    commentId: string,
  ): void | Promise<void>;

  abstract hasCommentBeenSent(
    sessionId: string,
    commentId: string,
  ): boolean | Promise<boolean>;

  abstract updateHeartbeat(userId: string): void;

  abstract addDailyUsage(value: number, userId: string): void;

  abstract getDailyUsage(userId: string): Promise<number>;

  bindDisconnectToSessionId(sessionId: string, value: any): void {
    StreamHubMemory.set(`${sessionId}::DISCONNECT`, value);
  }

  getDisconnectBySessionId(sessionId: string): any {
    return StreamHubMemory.get(`${sessionId}::DISCONNECT`);
  }
}

import { Injectable } from '@nestjs/common';
import { StreamHubRedisWatcher } from '@module/stream-hub/watchers/providers/redis/stream-hub-redis.watcher';
import { DEFAULT_STREAM_HUB_STORAGE_PROVIDER } from '@module/stream-hub/constants';

@Injectable()
export class StreamHubProviderWatcher {
  constructor(private readonly streamHubRedisWatcher: StreamHubRedisWatcher) {}

  getProvider() {
    if (DEFAULT_STREAM_HUB_STORAGE_PROVIDER === 'redis') {
      return this.streamHubRedisWatcher;
    }
    throw new Error(
      `Storage provider ${DEFAULT_STREAM_HUB_STORAGE_PROVIDER} not supported`,
    );
  }
}

import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { Socket } from 'socket.io';
import {
  StreamHubClientComment,
  StreamHubInput,
} from '@module/stream-hub/types';
import { CommentMapperFactory } from '@module/live-comment/mappers/comment-mapper.factory';
import { LivestreamPlatform } from '@common/constants';
import { StreamHubProviderWatcher } from '@module/stream-hub/watchers/stream-hub-provider.watcher';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { sleep } from '@common/utils';
import { LiveSessionService } from '@module/live-session/live-session.service';

@Injectable()
export class StreamHubCommentService {
  protected socket: Socket;
  protected config: StreamHubInput;
  protected platform: LivestreamPlatform;
  protected roomId: string;
  protected sessionId: string;

  constructor(
    private readonly logger: AppLogger,
    private readonly streamHubProviderWatcher: StreamHubProviderWatcher,
    private readonly platformUserService: PlatformUserService,
    private readonly liveSessionService: LiveSessionService,
  ) {
    this.logger.setContext(StreamHubCommentService.name);
  }

  setSocket(socket: Socket): void {
    this.socket = socket;
  }

  setConfig(config: StreamHubInput): void {
    this.config = config;
  }

  setPlatform(platform: LivestreamPlatform): void {
    this.platform = platform;
  }

  setRoomId(roomId: string): void {
    this.roomId = roomId;
  }

  setSessionId(sessionId: string): void {
    this.sessionId = sessionId;
  }

  init(params: {
    socket: Socket;
    config: StreamHubInput;
    platform: LivestreamPlatform;
    roomId: string;
    sessionId: string;
  }): void {
    this.setSocket(params.socket);
    this.setConfig(params.config);
    this.setPlatform(params.platform);
    this.setRoomId(params.roomId);
    this.setSessionId(params.sessionId);
  }

  async mapExtraInformationToComment(
    data: StreamHubClientComment,
  ): Promise<StreamHubClientComment> {
    // @ts-ignore
    const userId: string =
      // @ts-ignore
      data?.author?.channelId || data?.userId || data?.profileId;

    if (!userId) return data;

    const platformUser = await this.platformUserService.getUserById(userId);

    data.phoneNumber = platformUser?.phoneNumber || '';
    data.hasCanceledOrder = platformUser?.hasCanceledOrder || false;
    data.isOldCustomer = !!platformUser;
  }

  sendToClient(data: StreamHubClientComment): void {
    if (!this.socket) {
      this.logger.error('Socket is not initialized.');
      return;
    }

    const socketId = this.socket.id;

    this.socket.emit(`${socketId}-${this.config.platformChannelId}`, data);
    this.socket.emit(`FOR_ALL_CHANNEL`, data);
  }

  async sendPreviousComments(roomId?: string): Promise<void> {
    this.logger.log(
      `Fetching previous comments for room ID ${roomId || this.roomId}.`,
    );
    const previousComments =
      await this.liveSessionService.getCommentsByLiveSessionId(
        roomId || this.roomId,
        // userId,
      );

    if (previousComments.length > 0) {
      this.logger.log(
        `Sending ${previousComments.length} previous comments for room ID ${this.roomId} to socket ${this.socket.id}.`,
      );
      for (const comment of previousComments) {
        const commentMapperFactory = new CommentMapperFactory().init(
          this.platform,
        );
        this.sendToClient(commentMapperFactory.fromEntityToClient(comment));

        await this.streamHubProviderWatcher
          .getProvider()
          .addCommentHasBeenSent(this.sessionId, comment.commentId);

        await sleep(50);
      }
    }
  }
}

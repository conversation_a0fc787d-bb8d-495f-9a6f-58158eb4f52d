export class StreamHubMemory {
  static instances: Map<string, any> = new Map();

  static set(key: string, value: any): void {
    this.instances.set(key, value);
  }

  static get<T>(key: string): T {
    return this.instances.get(key);
  }

  static remove(key: string) {
    if (this.instances.has(key)) {
      this.instances.delete(key);
    }
  }

  static has(key: string): boolean {
    return this.instances.has(key);
  }
}

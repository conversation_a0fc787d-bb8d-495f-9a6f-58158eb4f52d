import { YoutubeClientComment } from '@module/live-comment/types/youtube';
import { TikTokClientComment } from '@module/live-comment/types/tiktok';
import { FacebookClientComment } from '@module/live-comment/types/facebook';

export interface StreamHubInput {
  platformChannelId: string;
  userId: string;
  saveHistory?: boolean;
}

export interface StreamHubOutput {
  roomId: string;
  platformChannelId: string;
  socketId: string;
  userId: string;
  sessionId: string;
}

export type StreamHubClientComment =
  | YoutubeClientComment
  | TikTokClientComment
  | FacebookClientComment;

export type StreamHubStorageProvider = 'redis' | 'memory';

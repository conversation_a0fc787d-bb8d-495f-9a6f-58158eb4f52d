import { BaseStreamPlatform } from '@module/stream-hub/platforms/base-stream-platform';
import { LivestreamPlatform } from '@common/constants';
import { Injectable } from '@nestjs/common';
import { LiveSessionDocument } from '@common/schemas';
import { io, Socket as SocketClient } from 'socket.io-client';
import { FacebookCommentMapper } from '@module/live-comment/mappers/facebook-comment.mapper';
import { HEADLESS_SERVER } from '@common/config/enviroment.config';
import {
  FacebookClientComment,
  FacebookPlatformComment,
} from '@module/live-comment/types/facebook';

@Injectable()
export class FacebookStreamPlatform extends BaseStreamPlatform {
  private commentMapper: FacebookCommentMapper = new FacebookCommentMapper();

  get name() {
    return LivestreamPlatform.FACEBOOK;
  }

  async handle() {
    const client = this.getClient();

    const roomId = await this.fetchRoomId();

    this.streamHubProviderWatcher
      .getProvider()
      .bindDisconnectToSessionId(this.getSessionId(), () =>
        client.disconnect(),
      );

    await this.streamHubCommentService.sendPreviousComments();

    // Trigger start on headless server
    client.emit('live', {
      platform: LivestreamPlatform.FACEBOOK,
      id: roomId,
      userId: this.getConfig().userId,
    });

    let liveSession: LiveSessionDocument;

    if (this.getConfig().saveHistory) {
      liveSession = await this.liveSessionService.start(this.getSessionId(), {
        userId: this.getConfig().userId,
        roomId,
        deviceId: this.getSocket().id,
        roomTitle: `Livestream Facebook ngày ${new Date().toLocaleDateString()}`,
        shareLink: `https://www.facebook.com/${roomId}`,
        livestreamPlatform: this.name,
        platformChannelId: this.getConfig().platformChannelId,
      });
    }

    client.on('chat', async (data: FacebookPlatformComment) => {
      const hasSent = await this.streamHubProviderWatcher
        .getProvider()
        .hasCommentBeenSent(this.getSessionId(), data.commentid);

      if (!hasSent) {
        const comment: FacebookClientComment = {
          commentId: data.commentid,
          userId: data.profileId,
          name: data.chatname,
          picture: data.chatimg,
          message: data.chatmessage,
          createTime: data.createdAt,
          phoneNumber: '',
          isOldCustomer: false,
          hasCanceledOrder: false,
        };

        await this.streamHubCommentService.mapExtraInformationToComment(
          comment,
        );

        this.streamHubCommentService.sendToClient(comment);

        await this.streamHubProviderWatcher
          .getProvider()
          .addCommentHasBeenSent(this.getSessionId(), data.commentid);

        if (liveSession) {
          const platformUser = await this.platformUserService.upsertUser(
            this.commentMapper.fromPlatformToPlatformUser(data),
          );
          await this.commentService.addCommentToLive({
            commentId: data.commentid,
            liveSessionId: liveSession.id,
            roomId: liveSession.roomId,
            message: data.chatmessage,
            platformUserId: platformUser.id,
          });
        }
      }
    });

    client.on('disconnect', async () => {
      this.streamHubProviderWatcher
        .getProvider()
        .triggerSessionStop(this.getSessionId());
    });

    client.on('connect_error', async error => {
      this.streamHubProviderWatcher
        .getProvider()
        .triggerSessionStop(this.getSessionId());
    });
  }

  getClient(): SocketClient {
    return io(HEADLESS_SERVER);
  }
}

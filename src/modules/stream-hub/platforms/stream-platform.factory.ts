import { Injectable } from '@nestjs/common';
import { LivestreamPlatform } from '@common/constants';
import { FacebookStreamPlatform } from '@module/stream-hub/platforms/facebook-stream-platform';
import { BaseStreamPlatform } from '@module/stream-hub/platforms/base-stream-platform';
import { AppLogger } from '@common/logger/logger.service';
import { PlatformChannelService } from '@module/platform-channel/services/platform-channel.service';
import { StreamHubCommentService } from '@module/stream-hub/services/stream-hub-comment.service';
import { StreamHubProviderWatcher } from '@module/stream-hub/watchers/stream-hub-provider.watcher';
import { ConfigService } from '@nestjs/config';
import { YoutubeStreamPlatform } from '@module/stream-hub/platforms/youtube-stream-platform';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { CommentService } from '@module/comment/comment.service';
import { SystemConfigService } from '@module/system-config/system-config.service';
import { InjectModel } from '@nestjs/mongoose';
import {
  PlatformChannel,
  PlatformChannelDocument,
} from '@common/schemas/platform-channel.schema';
import { Model } from 'mongoose';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { TiktokStreamPlatform } from '@module/stream-hub/platforms/tiktok-stream-platform';

@Injectable()
export class StreamPlatformFactory {
  constructor(
    private readonly logger: AppLogger,
    private readonly systemConfigService: SystemConfigService,
    private readonly platformUserService: PlatformUserService,
    private readonly platformChannelService: PlatformChannelService,
    private readonly commentService: CommentService,
    private readonly streamHubProviderStorage: StreamHubProviderWatcher,
    private readonly liveSessionService: LiveSessionService,
    private readonly configService: ConfigService,
    @InjectModel(PlatformChannel.name)
    private readonly platformChannelModel: Model<PlatformChannelDocument>,
  ) {}

  create(platform: LivestreamPlatform): BaseStreamPlatform {
    switch (platform) {
      case LivestreamPlatform.TIKTOK:
        return new TiktokStreamPlatform(
          this.logger,
          this.systemConfigService,
          this.platformUserService,
          this.platformChannelService,
          new StreamHubCommentService(
            this.logger,
            this.streamHubProviderStorage,
            this.platformUserService,
            this.liveSessionService,
          ),
          this.commentService,
          this.streamHubProviderStorage,
          this.liveSessionService,
          this.configService,
          this.platformChannelModel,
        );
      case LivestreamPlatform.YOUTUBE:
        return new YoutubeStreamPlatform(
          this.logger,
          this.systemConfigService,
          this.platformUserService,
          this.platformChannelService,
          new StreamHubCommentService(
            this.logger,
            this.streamHubProviderStorage,
            this.platformUserService,
            this.liveSessionService,
          ),
          this.commentService,
          this.streamHubProviderStorage,
          this.liveSessionService,
          this.configService,
          this.platformChannelModel,
        );
      case LivestreamPlatform.FACEBOOK:
        return new FacebookStreamPlatform(
          this.logger,
          this.systemConfigService,
          this.platformUserService,
          this.platformChannelService,
          new StreamHubCommentService(
            this.logger,
            this.streamHubProviderStorage,
            this.platformUserService,
            this.liveSessionService,
          ),
          this.commentService,
          this.streamHubProviderStorage,
          this.liveSessionService,
          this.configService,
          this.platformChannelModel,
        );
      default:
        throw new Error(`Platform ${platform} not supported`);
    }
  }
}

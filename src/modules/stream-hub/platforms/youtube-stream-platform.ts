import { BaseStreamPlatform } from '@module/stream-hub/platforms/base-stream-platform';
import { LivestreamPlatform } from '@common/constants';
import { Injectable } from '@nestjs/common';
import {
  AddChatItemAction,
  Masterchat,
  MasterchatError,
  MasterchatOptions,
} from 'masterchat';
import { YoutubeCommentMapper } from '@module/live-comment/mappers/youtube-comment.mapper';
import { YoutubeClientComment } from '@module/live-comment/types/youtube';
import { LiveSessionDocument } from '@common/schemas';

@Injectable()
export class YoutubeStreamPlatform extends BaseStreamPlatform {
  private commentMapper: YoutubeCommentMapper = new YoutubeCommentMapper();

  get name() {
    return LivestreamPlatform.YOUTUBE;
  }

  async getClient(): Promise<Masterchat> {
    const options: MasterchatOptions = {};

    if (this.configService.get('YOUTUBE_CREDENTIALS')) {
      options.credentials = this.configService.get('YOUTUBE_CREDENTIALS');
    }

    const roomId = await this.fetchRoomId();

    return Masterchat.init(roomId, options);
  }

  async handle() {
    const client = await this.getClient();

    const roomId = await this.fetchRoomId();

    this.streamHubProviderWatcher
      .getProvider()
      .bindDisconnectToSessionId(this.getSessionId(), () => client.stop());

    await this.streamHubCommentService.sendPreviousComments();

    let liveSession: LiveSessionDocument;

    if (this.getConfig().saveHistory) {
      liveSession = await this.liveSessionService.start(this.getSessionId(), {
        userId: this.getConfig().userId,
        roomId,
        deviceId: this.getSocket().id,
        roomTitle: client.title,
        shareLink: `https://www.youtube.com/watch?v=${client.videoId}`,
        livestreamPlatform: this.name,
        platformChannelId: this.getConfig().platformChannelId,
      });
    }

    client.on('chat', async (data: AddChatItemAction) => {
      const comment = this.convertChat(data);
      const hasSent = await this.streamHubProviderWatcher
        .getProvider()
        .hasCommentBeenSent(this.getSessionId(), comment.id);

      if (!hasSent) {
        await this.streamHubCommentService.mapExtraInformationToComment(
          comment,
        );
        this.streamHubCommentService.sendToClient(comment);

        await this.streamHubProviderWatcher
          .getProvider()
          .addCommentHasBeenSent(this.getSessionId(), comment.id);

        if (liveSession) {
          const platformUser = await this.platformUserService.upsertUser(
            this.commentMapper.fromPlatformToPlatformUser(data),
          );

          await this.commentService.addCommentToLive({
            commentId: comment.id,
            liveSessionId: liveSession.id,
            roomId: liveSession.roomId,
            message: this.commentMapper.fromYTCommentToText(data.message),
            platformUserId: platformUser.id,
          });
        }
      }
    });

    client.on('error', (error: MasterchatError) => {
      this.streamHubProviderWatcher
        .getProvider()
        .triggerSessionStop(this.getSessionId());
    });

    client.on('end', () => {
      this.streamHubProviderWatcher
        .getProvider()
        .triggerSessionStop(this.getSessionId());
    });

    await client.listen();
  }

  protected convertChat(chatItem: AddChatItemAction): YoutubeClientComment {
    if (!chatItem || !chatItem.authorName || !chatItem.message) {
      this.logger.warn(
        `Invalid chat item received: ${JSON.stringify(chatItem)}`,
      );
      return null;
    }

    return {
      id: chatItem.id,
      author: {
        name: chatItem.authorName,
        thumbnail: {
          url: chatItem.authorPhoto,
          alt: '',
        },
        channelId: chatItem.authorChannelId,
        badge: {
          thumbnail: {
            url: chatItem.authorPhoto,
            alt: '',
          },
          label: '',
        },
      },
      message: this.commentMapper.parseMessages(chatItem.message),
      isMembership: Boolean(chatItem.membership),
      isVerified: chatItem.isVerified,
      isOwner: chatItem.isOwner,
      isModerator: chatItem.isModerator,
      timestamp: chatItem.timestamp,
      isOldCustomer: false,
      hasCanceledOrder: false,
      phoneNumber: '',
    };
  }
}

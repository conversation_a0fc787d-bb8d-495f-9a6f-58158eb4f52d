import { StreamHubStorageProvider } from '@module/stream-hub/types';

export enum StreamHubEvent {
  STREAM_STARTED = 'stream_started',
  STREAM_ENDED = 'stream_ended',
  MESSAGE_RECEIVED = 'message_received',
  USER_JOINED = 'user_joined',
  LIVE_ERROR = 'live_error',
  STOP_SESSION = 'stop_session',
  STOP_PLATFORM_CHANNEL = 'stop_platform_channel',
  STOP_SOCKET = 'stop_socket',
}

export const DEFAULT_STREAM_HUB_STORAGE_PROVIDER: StreamHubStorageProvider =
  'redis';

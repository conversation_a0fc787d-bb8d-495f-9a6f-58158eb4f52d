import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsOptional,
  IsString,
  MaxLength,
  <PERSON>Length,
  Matches,
} from 'class-validator';

export class CreateContactDto {
  @ApiPropertyOptional({
    description: 'Contact name',
    example: '<PERSON>',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '+1234567890',
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @Matches(/^[\+]?[0-9\s\-\(\)]{7,20}$/, {
    message: 'Phone number must be a valid format',
  })
  phoneNumber?: string;
}

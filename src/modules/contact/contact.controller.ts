import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ContactService } from './contact.service';
import { ContactDto } from './dtos/contact.dto';
import { CreateContactDto } from './dtos/create-contact.dto';
import { RateLimit, RateLimitGuard } from '@common/guards/rate-limit.guard';

@ApiTags('Contact Requests')
@Controller('contacts')
export class ContactController {
  constructor(private readonly contactService: ContactService) {}

  @Post('request')
  @UseGuards(RateLimitGuard)
  @RateLimit({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 3, // 3 requests per minute for public users
    keyGenerator: req => `contact_request:${req.ip}`, // Rate limit by IP since no auth
  })
  @ApiOperation({
    summary: 'Submit a contact request',
    description:
      'Public endpoint for clients to submit contact requests. No authentication required.',
  })
  @ApiResponse({
    status: 201,
    description: 'Contact request submitted successfully',
    type: ContactDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests - rate limit exceeded',
  })
  async submitContactRequest(
    @Body() createContactDto: CreateContactDto,
  ): Promise<ContactDto> {
    // Public contact request - no user association needed
    return this.contactService.create(createContactDto);
  }
}

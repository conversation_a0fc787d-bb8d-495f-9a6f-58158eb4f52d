import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { ContactService } from './contact.service';
import { ContactController } from './contact.controller';
import { AdminContactController } from './admin-contact.controller';
import { Contact, ContactSchema } from '@common/schemas/contact.schema';
import { SystemUser, SystemUserSchema } from '@common/schemas';
import { SchemaCollectionName } from '@common/constants/schema';
import { LoggerModule } from '@common/logger/logger.module';
import { SharedModule } from '@shared/shared.module';
import { RedisModule } from '@shared/redis/redis.module';
import { AdminGuard } from '@common/guards/admin.guard';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    SharedModule,
    RedisModule,
    MongooseModule.forFeature([
      {
        name: Contact.name,
        schema: ContactSchema,
        collection: SchemaCollectionName.Contact,
      },
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
    ]),
  ],
  controllers: [ContactController, AdminContactController],
  providers: [ContactService, AdminGuard],
  exports: [ContactService],
})
export class ContactModule {}

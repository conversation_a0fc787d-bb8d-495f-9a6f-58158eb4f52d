import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ContactService } from './contact.service';
import { ContactDto } from './dtos/contact.dto';
import { CreateContactDto } from './dtos/create-contact.dto';
import { UpdateContactDto } from './dtos/update-contact.dto';
import { SearchContactDto } from './dtos/search-contact.dto';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { UserPayload } from '@module/user/types';
import { PaginationResultDto } from '@shared/dtos/pagination.dto';
import { AdminGuard } from '@common/guards/admin.guard';

@ApiTags('Contact Management for Admin')
@Controller('/admin/contacts')
@ApiBearerAuth()
export class AdminContactController {
  constructor(private readonly contactService: ContactService) {}

  @Post()
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Create a new contact' })
  @ApiResponse({
    status: 201,
    description: 'Contact created successfully',
    type: ContactDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  async create(
    @Body() createContactDto: CreateContactDto,
  ): Promise<ContactDto> {
    return this.contactService.create(createContactDto);
  }

  @Get()
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get all contacts (admin access)' })
  @ApiResponse({
    status: 200,
    description: 'Contacts retrieved successfully',
    type: PaginationResultDto<ContactDto>,
  })
  async findAll(
    @Query() searchDto: SearchContactDto,
  ): Promise<PaginationResultDto<ContactDto>> {
    return this.contactService.findAll(searchDto);
  }

  @Get(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get a specific contact by ID (admin access)' })
  @ApiParam({
    name: 'id',
    description: 'Contact ID',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Contact retrieved successfully',
    type: ContactDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contact not found',
  })
  async findOne(@Param('id') id: string): Promise<ContactDto> {
    return this.contactService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update a contact (admin access)' })
  @ApiParam({
    name: 'id',
    description: 'Contact ID',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Contact updated successfully',
    type: ContactDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contact not found',
  })
  async update(
    @Param('id') id: string,
    @Body() updateContactDto: UpdateContactDto,
  ): Promise<ContactDto> {
    return this.contactService.update(id, updateContactDto);
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Delete a contact (admin access)' })
  @ApiParam({
    name: 'id',
    description: 'Contact ID',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Contact deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Contact not found',
  })
  async remove(@Param('id') id: string): Promise<{ message: string }> {
    await this.contactService.remove(id);
    return { message: 'Contact deleted successfully' };
  }
}

import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery } from 'mongoose';
import { AppLogger } from '@common/logger/logger.service';
import { PaginationService } from '@shared/services/pagination.service';
import { PaginationResultDto } from '@shared/dtos/pagination.dto';
import { Contact, ContactDocument } from '@common/schemas/contact.schema';
import { ContactDto } from './dtos/contact.dto';
import { CreateContactDto } from './dtos/create-contact.dto';
import { UpdateContactDto } from './dtos/update-contact.dto';
import { SearchContactDto } from './dtos/search-contact.dto';

@Injectable()
export class ContactService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(Contact.name)
    private readonly contactModel: Model<ContactDocument>,
    private readonly paginationService: PaginationService,
  ) {
    this.logger.setContext(ContactService.name);
  }

  async create(createContactDto: CreateContactDto): Promise<ContactDto> {
    this.logger.log('Creating new contact');

    // Validate that at least one field is provided
    if (
      !createContactDto.name &&
      !createContactDto.email &&
      !createContactDto.phoneNumber
    ) {
      throw new ForbiddenException(
        'At least one contact field (name, email, or phone number) must be provided',
      );
    }

    const contact = new this.contactModel({
      ...createContactDto,
    });

    await contact.save();
    this.logger.log(`Contact created with ID: ${contact.id}`);

    return this.toDto(contact);
  }

  async findAll(
    searchDto: SearchContactDto,
  ): Promise<PaginationResultDto<ContactDto>> {
    this.logger.log('Finding all contacts');

    const query: FilterQuery<ContactDocument> = {};

    // Apply search filters
    if (searchDto.search) {
      query.$text = { $search: searchDto.search };
    }

    if (searchDto.name) {
      query.name = { $regex: searchDto.name, $options: 'i' };
    }

    if (searchDto.email) {
      query.email = { $regex: searchDto.email, $options: 'i' };
    }

    if (searchDto.phoneNumber) {
      query.phoneNumber = { $regex: searchDto.phoneNumber, $options: 'i' };
    }

    const result = await this.paginationService.paginate(
      this.contactModel,
      query,
      searchDto,
    );

    return {
      data: result.data.map(contact => this.toDto(contact)),
      meta: result.meta,
    };
  }

  async findOne(contactId: string): Promise<ContactDto> {
    this.logger.log(`Finding contact ${contactId}`);

    const contact = await this.contactModel
      .findOne({
        id: contactId,
      })
      .exec();

    if (!contact) {
      throw new NotFoundException(`Contact with ID ${contactId} not found`);
    }

    return this.toDto(contact);
  }

  async update(
    contactId: string,
    updateContactDto: UpdateContactDto,
  ): Promise<ContactDto> {
    this.logger.log(`Updating contact ${contactId}`);

    const contact = await this.contactModel
      .findOne({
        id: contactId,
      })
      .exec();

    if (!contact) {
      throw new NotFoundException(`Contact with ID ${contactId} not found`);
    }

    // Validate that at least one field will remain after update
    const updatedContact = { ...contact.toObject(), ...updateContactDto };
    if (
      !updatedContact.name &&
      !updatedContact.email &&
      !updatedContact.phoneNumber
    ) {
      throw new ForbiddenException(
        'At least one contact field (name, email, or phone number) must be provided',
      );
    }

    Object.assign(contact, updateContactDto);
    contact.updatedAt = new Date();
    await contact.save();

    this.logger.log(`Contact ${contactId} updated successfully`);
    return this.toDto(contact);
  }

  async remove(contactId: string): Promise<void> {
    this.logger.log(`Removing contact ${contactId}`);

    const result = await this.contactModel
      .deleteOne({
        id: contactId,
      })
      .exec();

    if (result.deletedCount === 0) {
      throw new NotFoundException(`Contact with ID ${contactId} not found`);
    }

    this.logger.log(`Contact ${contactId} removed successfully`);
  }

  async getContactCount(): Promise<number> {
    return this.contactModel.countDocuments({}).exec();
  }

  private toDto(contact: ContactDocument): ContactDto {
    return new ContactDto({
      id: contact.id,
      name: contact.name,
      email: contact.email,
      phoneNumber: contact.phoneNumber,
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
    });
  }
}

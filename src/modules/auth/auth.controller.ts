import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthService } from './services/auth.service';
import { LoginRequestDto } from './dtos/login.dto';
import { RegisterDto } from './dtos/register.dto';
import { Auth, CurrentUser } from '@common/decorators';
import { TokenResponseDto } from '@module/auth/dtos/token.dto';
import { GetRefreshTokenDto } from '@module/auth/dtos/get-refresh-token.dto';
import { UserPayload } from '@module/user/types';
import { LiveSessionPaginationResultDto } from '@module/live-session/dtos/live-session-pagination.dto';
import {
  PaginationOptionsDto,
  CursorPaginationOptionsDto,
} from '@shared/dtos/pagination.dto';
import { GetLiveSessionDto } from '@module/live-session/dtos/get-live-session.dto';
import { LiveSessionService } from '@module/live-session/live-session.service';
import {
  CommentPaginationResultDto,
  CommentCursorPaginationResultDto,
} from '@module/comment/dtos/comment-pagination.dto';

@ApiTags('Auth')
@Controller('/auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly liveSessionService: LiveSessionService,
  ) {}

  @Auth()
  @Get('me')
  async me(@CurrentUser() currentUser: UserPayload): Promise<any> {
    return {
      id: currentUser.id,
      email: currentUser.email,
      username: currentUser.username,
      role: currentUser.role,
    };
  }

  @ApiOkResponse({ type: TokenResponseDto })
  @Post('login')
  async login(@Body() loginData: LoginRequestDto): Promise<TokenResponseDto> {
    return this.authService.login(loginData.username, loginData.password);
  }

  @Post('register')
  async register(@Body() registerData: RegisterDto): Promise<TokenResponseDto> {
    return this.authService.register(registerData);
  }

  @ApiOkResponse({ type: TokenResponseDto })
  @Post('refresh')
  refreshToken(@Body() body: GetRefreshTokenDto): Promise<TokenResponseDto> {
    const { refreshToken } = body;
    return this.authService.refreshToken(refreshToken);
  }

  @Auth()
  @Delete('delete-account')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete the current user account' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Account successfully deleted',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  async deleteAccount(
    @CurrentUser() currentUser: UserPayload,
  ): Promise<{ success: boolean; message: string }> {
    return this.authService.deleteAccount(currentUser.id);
  }

  @Auth()
  @Get('/live-sessions')
  @ApiOkResponse({ type: LiveSessionPaginationResultDto })
  myLiveSessions(
    @Query() paginationOptions: PaginationOptionsDto,
    @Query() filters: GetLiveSessionDto,
    @CurrentUser() user: UserPayload,
  ) {
    return this.liveSessionService.findMyLiveSessions(
      user.id,
      paginationOptions,
      filters,
    );
  }

  @Auth()
  @Get('/live-sessions/:id/comments')
  @ApiOkResponse({ type: CommentPaginationResultDto })
  myLiveSessionComments(
    @Param('id') id: string,
    @Query() paginationOptions: PaginationOptionsDto,
  ) {
    return this.liveSessionService.findMyLiveSessionCommentsByLiveSessionId(
      id,
      paginationOptions,
    );
  }

  @Auth()
  @Get('/live-sessions/:id/comments/cursor')
  @ApiOkResponse({ type: CommentCursorPaginationResultDto })
  myLiveSessionCommentsWithCursor(
    @Param('id') id: string,
    @Query() cursorOptions: CursorPaginationOptionsDto,
  ) {
    return this.liveSessionService.findMyLiveSessionCommentsByLiveSessionIdWithCursor(
      id,
      cursorOptions,
    );
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    description: 'Username of the user',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(4)
  username: string;

  @ApiPropertyOptional({
    description: 'Email of the user',
    type: 'string',
  })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Password of the user',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  password: string;
}

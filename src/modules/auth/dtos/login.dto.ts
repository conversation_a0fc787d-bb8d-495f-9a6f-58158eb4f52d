import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class LoginRequestDto {
  @ApiProperty({
    description: 'Username of the user',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(4)
  username: string;

  @ApiProperty({
    description: 'Password of the user',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  password: string;
}

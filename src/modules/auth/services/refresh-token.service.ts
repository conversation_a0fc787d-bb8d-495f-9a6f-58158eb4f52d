import { v4 as uuidv4 } from 'uuid';
import { Request } from 'express';

import { JwtService } from '@nestjs/jwt';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { RefreshToken } from '@common/schemas/refresh-token.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class RefreshTokenService {
  readonly expiresIn = process.env.JWT_REFRESH_EXPIRES_IN;
  readonly issuer = process.env.JWT_REFRESH_ISSUER;
  readonly secret = process.env.JWT_REFRESH_SECRET;

  constructor(
    private readonly jwtService: JwtService,
    @InjectModel(RefreshToken.name)
    private refreshTokenModel: Model<RefreshToken>,
  ) {}

  /**
   * Generate a refresh token, bind it with the given user profile + access
   * token, then store them in backend.
   */
  async generateToken(
    userId: string,
    token: string,
    clientInfo: any,
  ): Promise<string> {
    const data = {
      token: uuidv4(),
    };
    const refreshToken = await this.jwtService.signAsync(data, {
      expiresIn: this.expiresIn,
      issuer: this.issuer,
      secret: this.secret,
    });

    await this.refreshTokenModel.create({
      ...clientInfo,
      userId,
      token: refreshToken,
      currentToken: token,
    });

    return refreshToken;
  }

  // async revokeCurrentToken(token: string): Promise<void> {
  //   try {
  //     // revoke old assess token if token is valid
  //     if (token) {
  //       await this.accessTokenService.verifyToken(token);
  //
  //       await this.accessTokenService.revokeToken(token);
  //     }
  //   } catch (e) {
  //     // ignore
  //   }
  // }

  async revokeToken(refreshToken: string): Promise<void> {
    try {
      await this.refreshTokenModel.updateOne(
        { token: refreshToken },
        { isRevoked: true, revokedAt: new Date() },
      );
    } catch (error) {
      // ignore
    }
  }

  /**
   * Verify the validity of a refresh token, and make sure it exists in backend.
   * @param refreshToken
   */
  async verifyToken(refreshToken: string): Promise<RefreshToken> {
    try {
      await this.jwtService.verifyAsync(refreshToken, { secret: this.secret });

      const userRefreshData = await this.refreshTokenModel.findOne({
        token: refreshToken,
      });

      if (!userRefreshData) {
        throw new Error(`Error verifying token : Invalid Token`);
      }

      if (userRefreshData.isRevoked) {
        throw new Error(`Error verifying token : Token is revoked`);
      }

      return userRefreshData;
    } catch (error) {
      const { message } = error as { message: string };
      throw new UnauthorizedException(`Error verifying token : ${message}`);
    }
  }

  updateToken(refreshToken: string, token: string): Promise<any> {
    return this.refreshTokenModel.updateOne(
      { token: refreshToken },
      { currentToken: token },
    );
  }

  extractTokenFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];

    return type === 'Refresh' ? token : null;
  }
}

import { AppLogger } from '@common/logger/logger.service';
import { SubscriptionPlan, SystemUser } from '@common/schemas';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { RegisterDto } from '../dtos/register.dto';
import { AppException } from '@common/exceptions/app-exception';
import {
  ACCOUNT_DELETION_ERROR,
  PASSWORD_NOT_CORRECT,
  USER_ALREADY_EXIST,
  USER_NOT_EXIST,
} from '@common/exceptions/error';
import * as bcrypt from 'bcryptjs';
import { JwtService } from '@nestjs/jwt';
import { ENV } from '@common/config';
import { RefreshTokenService } from '@module/auth/services/refresh-token.service';
import { TokenResponseDto } from '@module/auth/dtos/token.dto';
import { SubscriptionService } from '@module/subscription/subscription.service';
import { RefreshToken } from '@common/schemas/refresh-token.schema';

@Injectable()
export class AuthService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(SystemUser.name)
    private readonly systemUserModel: Model<SystemUser>,
    @InjectModel(RefreshToken.name)
    private readonly refreshTokenModel: Model<RefreshToken>,
    private readonly jwtService: JwtService,
    private readonly refreshTokenService: RefreshTokenService,
    private readonly subscriptionService: SubscriptionService,
  ) {
    this.logger.setContext(AuthService.name);
  }

  async login(username: string, password: string): Promise<TokenResponseDto> {
    const user = await this.__findUserByUsername(username);
    if (!user) {
      throw new AppException(USER_NOT_EXIST);
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      throw new AppException(PASSWORD_NOT_CORRECT);
    }

    return this.generateToken(user);
  }

  async register(registerData: RegisterDto): Promise<TokenResponseDto> {
    const existUser = await this.__findUserByUsername(registerData.username);
    if (existUser) {
      throw new AppException(USER_ALREADY_EXIST);
    }

    const newUser = await this.__registerNewUser(registerData);
    return this.login(newUser.username, registerData.password);
  }

  async refreshToken(
    refreshToken: string | undefined,
  ): Promise<TokenResponseDto> {
    try {
      if (!refreshToken) {
        throw new Error(`Error verifying token : 'refresh token' is null`);
      }

      const userRefreshData = await this.refreshTokenService.verifyToken(
        refreshToken,
      );

      const user = await this.systemUserModel
        .findOne({ id: userRefreshData.userId })
        .exec();

      await this.refreshTokenService.revokeToken(refreshToken);

      return this.generateToken(user);
    } catch (error) {
      const { message } = error as { message?: string };

      throw new UnauthorizedException(`Error verifying token : ${message}`);
    }
  }

  private async __findUserByUsername(username: string): Promise<SystemUser> {
    return this.systemUserModel.findOne({ username }).exec();
  }

  private async __registerNewUser(
    registerData: RegisterDto,
  ): Promise<SystemUser> {
    const hashedPassword = await bcrypt.hash(registerData.password, 10);

    // Try to find the free subscription plan before creating the user
    let subscriptionId: string | null = null;
    try {
      const freeSubscription = await this.subscriptionService.findByPlan(
        SubscriptionPlan.FREE,
      );

      if (freeSubscription) {
        subscriptionId = freeSubscription.id;
        this.logger.log(
          `Found free subscription plan: ${freeSubscription.name}`,
        );
      } else {
        this.logger.warn('No free subscription plan found for new user');
      }
    } catch (error) {
      this.logger.error(
        `Error finding free subscription plan: ${error.message}`,
        error.stack,
      );
    }

    // Create the user with subscription ID if available
    const newUser = new this.systemUserModel({
      username: registerData.username,
      email: registerData.email,
      password: hashedPassword,
      subscriptionId: subscriptionId, // Set directly during user creation
    });

    // Save and return the user
    return newUser.save();
  }

  private async generateAccessToken(userId: string): Promise<string> {
    const payload = {
      sub: userId,
      userId: userId,
    };
    return await this.jwtService.signAsync(payload, {
      secret: ENV.JWT_CONFIG.secret,
      expiresIn: ENV.JWT_CONFIG.expiresIn,
    });
  }

  private async generateToken(user: SystemUser): Promise<TokenResponseDto> {
    const accessToken = await this.generateAccessToken(user.id);
    const refreshToken = await this.refreshTokenService.generateToken(
      user.id,
      accessToken,
      {},
    );

    return {
      accessToken,
      refreshToken,
    };
  }

  /**
   * Delete a user account
   * @param userId The ID of the user to delete
   * @returns The deleted user data
   */
  async deleteAccount(
    userId: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Find the user
      const user = await this.systemUserModel.findOne({ id: userId }).exec();
      if (!user) {
        throw new AppException(USER_NOT_EXIST);
      }

      // Revoke all refresh tokens for this user
      await this.refreshTokenModel.updateMany(
        { userId: userId },
        { isRevoked: true, revokedAt: new Date() },
      );

      // Delete the user
      const deletedUser = await this.systemUserModel
        .findOneAndDelete({ id: userId })
        .exec();
      if (!deletedUser) {
        throw new AppException(USER_NOT_EXIST);
      }

      this.logger.log(`User account deleted: ${userId}`);

      return {
        success: true,
        message: 'Your account has been successfully deleted',
      };
    } catch (error) {
      this.logger.error(
        `Error deleting account for user ${userId}: ${error.message}`,
        error.stack,
      );

      // If it's already an AppException, rethrow it
      if (error instanceof AppException) {
        throw error;
      }

      // Otherwise, wrap it in our custom error
      throw new AppException(ACCOUNT_DELETION_ERROR);
    }
  }
}

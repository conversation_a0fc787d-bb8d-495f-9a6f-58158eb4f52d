import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LiveManagerService } from './live-manager.service';
import { GetLiveCommentDto } from './dtos/get-live-comment.dto';
import { Auth } from '@common/decorators';

@ApiTags('LiveManager')
@Controller('/live-manager')
export class LiveManagerController {
  constructor(private readonly liveManagerService: LiveManagerService) {}

  @Auth()
  @Get('live-sessions/:username')
  async getLiveSession(@Param('username') username: string): Promise<any> {
    return this.liveManagerService.getLiveSessionByUsername(username);
  }

  @Auth()
  @Get('live-session/:liveSessionId/comments')
  async getLiveSessionComments(
    @Query() queryPagination: GetLiveCommentDto,
    @Param('liveSessionId') liveSessionId: string,
  ): Promise<any> {
    return this.liveManagerService.getCommentsByLiveSessionId(
      liveSessionId,
      queryPagination.limit,
      queryPagination.page,
    );
  }
}

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';

export class GetLiveCommentDto {
  @ApiPropertyOptional({
    description: 'Number of items per page',
    default: 20,
    type: 'string',
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit = 20;

  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
    type: 'string',
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page = 1;
}

export interface UserBadge {
  type: string;
  badgeSceneType: number;
  displayType?: number;
  url?: string;
  privilegeId?: string;
  level?: number;
}

export interface UserDetails {
  createTime: string;
  bioDescription?: string;
  profilePictureUrls?: string[];
}

export interface FollowInfo {
  followingCount?: number;
  followerCount?: number;
  followStatus?: number;
  pushStatus?: number;
}

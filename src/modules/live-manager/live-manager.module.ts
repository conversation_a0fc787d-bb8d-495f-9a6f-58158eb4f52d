import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { LiveManagerService } from './live-manager.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  PlatformUser,
  PlatformUserSchema,
  LiveSession,
  LiveSessionSchema,
} from '@common/schemas';
import { LiveManagerController } from './live-manager.controller';
import { PlatformUserModule } from '@module/platform-user/platform-user.module';
import { SchemaCollectionName } from '@common/constants/schema';

@Module({
  imports: [
    LoggerModule,
    MongooseModule.forFeature([
      {
        name: PlatformUser.name,
        schema: PlatformUserSchema,
        collection: SchemaCollectionName.PlatformUser,
      },
      {
        name: LiveSession.name,
        schema: LiveSessionSchema,
        collection: SchemaCollectionName.LiveSession,
      },
    ]),
    PlatformUserModule,
  ],
  controllers: [LiveManagerController],
  providers: [LiveManagerService],
  exports: [LiveManagerService],
})
export class LiveManagerModule {}

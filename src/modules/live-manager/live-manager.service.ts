import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { TempComment, LiveSession } from '@common/schemas';
import { PlatformUser } from '@common/schemas/platform-user.schema';
import { AppException } from '@common/exceptions/app-exception';
import { LIVE_NOT_EXIST } from '@common/exceptions/error';
import { PlatformUserService } from '@module/platform-user/platform-user.service';
import { LivestreamPlatform } from '@common/constants';

@Injectable()
export class LiveManagerService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(LiveSession.name)
    private liveSessionModel: Model<LiveSession>,
    @InjectModel(PlatformUser.name)
    private platformUserModel: Model<PlatformUser>,
    private readonly userService: PlatformUserService,
  ) {
    this.logger.setContext(LiveManagerService.name);
  }

  async getLiveSessionByUsername(username: string): Promise<LiveSession[]> {
    const liveSessions = await this.liveSessionModel
      .find({ username })
      .select('-comments')
      .exec();
    return liveSessions.map(
      session =>
        ({
          id: session.id,
          roomId: session.roomId,
          roomTitle: session.roomTitle,
          deviceId: session.deviceId,
          username: session.username,
          createdAt: session.createdAt,
        } as LiveSession),
    );
  }

  async createLiveSession(
    livestreamPlatform: LivestreamPlatform,
    deviceId: string,
    username: string,
    roomId: string,
    roomTitle: string,
    shareLink: string,
  ): Promise<string> {
    const newLiveSession = new this.liveSessionModel({
      id: uuidv4(),
      roomId: roomId,
      deviceId,
      username,
      roomTitle,
      shareLink,
      livestreamPlatform,
    });
    await newLiveSession.save();
    return newLiveSession.id;
  }

  async saveCommentToLiveSession(
    livestreamPlatform: LivestreamPlatform,
    liveSessionId: string,
    deviceId: string,
    responseComment: TempComment,
  ): Promise<void> {
    const liveSession = await this.liveSessionModel.findOne({
      id: liveSessionId,
      deviceId: deviceId,
    });

    if (!liveSession) {
      return;
    }

    let existUser = await this.platformUserModel.findOneAndUpdate(
      {
        userId: responseComment.user.userId,
        livestreamPlatform,
      },
      {
        $setOnInsert: {
          userId: responseComment.user.userId,
          secUid: responseComment.user.secUid,
          uniqueId: responseComment.user.uniqueId,
          nickname: responseComment.user.nickname,
          profilePictureUrl: responseComment.user.profilePictureUrl,
          livestreamPlatform,
        },
      },
      {
        upsert: true,
      },
    );

    if (!existUser) {
      existUser = await this.platformUserModel.findOne({
        userId: responseComment.user.userId,
      });
    }

    const newComment = {
      msgId: responseComment.msgId,
      comment: responseComment.comment,
      createTime: responseComment.createTime,
      user: existUser,
    } as TempComment;

    await this.liveSessionModel.findOneAndUpdate(
      { id: liveSessionId },
      {
        $push: { comments: newComment },
        $set: { updatedAt: new Date() },
      },
      { new: true },
    );
  }

  async getCommentsByLiveSessionId(
    liveSessionId: string,
    limit = 20,
    page = 1,
  ): Promise<{ total: number; comments: TempComment[] }> {
    const totalCommentsResult = await this.liveSessionModel
      .aggregate([
        { $match: { id: liveSessionId } },
        { $project: { totalComments: { $size: '$comments' } } },
      ])
      .exec();

    if (
      !totalCommentsResult ||
      (totalCommentsResult && totalCommentsResult.length === 0)
    ) {
      throw new AppException(LIVE_NOT_EXIST);
    }

    const comments = await this.liveSessionModel
      .aggregate([
        { $match: { id: liveSessionId } },
        { $unwind: '$comments' },
        { $sort: { 'comments.createTime': 1 } },
        { $skip: (page - 1) * limit },
        { $limit: limit },
        {
          $group: {
            _id: '$_id',
            comments: { $push: '$comments' },
          },
        },
      ])
      .exec();

    const sessionComments = comments[0]?.comments as any[];

    const userIds = sessionComments?.flatMap(comment => comment.user.userId);

    const users = await this.userService.getUsersByIds(userIds || []);
    const userMap = new Map<string, PlatformUser>();
    users.forEach(user => userMap.set(user.userId, user));

    sessionComments?.forEach(comment => {
      const existUser = userMap.get(comment.user.userId);
      if (existUser.phoneNumber) {
        comment.user.phoneNumber = existUser.phoneNumber;
        comment.isOldCustomer = true;
      } else {
        comment.user.phoneNumber = null;
        comment.isOldCustomer = false;
      }
    });

    return {
      total: totalCommentsResult[0].totalComments,
      comments: sessionComments || [],
    };
  }
}

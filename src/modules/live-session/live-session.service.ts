import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import {
  LiveSession,
  LiveSessionDocument,
  LiveSessionHistory,
} from '@common/schemas';
import { LivestreamPlatform } from '@common/constants';
import { Comment, CommentDocument } from '@common/schemas/comment.schema';
import { GetLiveSessionDto } from '@module/live-session/dtos/get-live-session.dto';
import {
  PaginationOptionsDto,
  CursorPaginationOptionsDto,
} from '@shared/dtos/pagination.dto';
import { PaginationService } from '@shared/services/pagination.service';
import { CursorPaginationService } from '@shared/services/cursor-pagination.service';
import { LiveSessionPaginationResultDto } from '@module/live-session/dtos/live-session-pagination.dto';
import {
  CommentPaginationResultDto,
  CommentCursorPaginationResultDto,
} from '@module/comment/dtos/comment-pagination.dto';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelDocument,
} from '@common/schemas/system-user-platform-channel.schema';
import { uniq } from 'lodash';

@Injectable()
export class LiveSessionService {
  constructor(
    private readonly logger: AppLogger,
    private readonly paginationService: PaginationService,
    private readonly cursorPaginationService: CursorPaginationService,
    @InjectModel(LiveSession.name)
    private readonly liveSessionModel: Model<LiveSessionDocument>,
    @InjectModel(SystemUserPlatformChannel.name)
    private readonly systemUserPlatformChannel: Model<SystemUserPlatformChannelDocument>,
    @InjectModel(Comment.name)
    private commentModel: Model<CommentDocument>,
  ) {
    this.logger.setContext(LiveSessionService.name);
  }

  async start(
    sessionId: string,
    data: {
      userId: string;
      roomId: string;
      deviceId: string;
      roomTitle: string;
      shareLink: string;
      platformChannelId: string;
      livestreamPlatform: LivestreamPlatform;
    },
  ): Promise<LiveSessionDocument> {
    const liveSession = await this.liveSessionModel.findOneAndUpdate(
      {
        platformChannelId: data.platformChannelId,
        roomId: data.roomId,
      },
      {
        $set: {
          roomTitle: data.roomTitle,
          shareLink: data.shareLink,
        },
        $setOnInsert: {
          roomId: data.roomId,
          deviceId: data.deviceId,
          platformChannelId: data.platformChannelId,
          livestreamPlatform: data.livestreamPlatform,
        },
      },
      {
        upsert: true,
        new: true,
      },
    );

    const newHistory = new LiveSessionHistory();
    newHistory.createdAt = new Date();
    newHistory.sessionId = sessionId;

    await this.liveSessionModel.findOneAndUpdate(
      { id: liveSession.id },
      {
        $push: { histories: newHistory },
        $set: { updatedAt: new Date() },
      },
      { new: true },
    );

    return liveSession;
  }

  async findMyLiveSessions(
    userId: string,
    options: PaginationOptionsDto,
    filters: GetLiveSessionDto,
  ): Promise<LiveSessionPaginationResultDto> {
    const systemUserPlatformChannelBulk =
      await this.systemUserPlatformChannel.find({
        systemUser: userId,
      });

    const platformChannelIds = uniq(
      systemUserPlatformChannelBulk.map(item =>
        item.platformChannel.toString(),
      ),
    );

    const filterQuery: FilterQuery<LiveSessionDocument> = {
      $and: [
        {
          platformChannelId: {
            $in: platformChannelIds,
          },
        },
      ],
    };

    if (filters.livestreamPlatform) {
      filterQuery.livestreamPlatform = filters.livestreamPlatform;
    }

    if (filters.search) {
      filterQuery.$or = [
        { roomTitle: { $regex: filters.search, $options: 'i' } },
      ];
    }

    if (filters.platformChannelId) {
      filterQuery.platformChannelId = filters.platformChannelId;
    }

    const result = await this.paginationService.paginate(
      this.liveSessionModel,
      filterQuery,
      options,
    );

    return {
      data: result.data.map(item => ({
        id: item.id,
        roomId: item.roomId,
        roomTitle: item.roomTitle,
        shareLink: item.shareLink,
        username: item.username,
        livestreamPlatform: item.livestreamPlatform,
        platformChannelId: item.platformChannelId,
        createdAt: item.createdAt,
      })),
      meta: result.meta,
    };
  }

  async findMyLiveSessionCommentsByLiveSessionId(
    liveSessionId: string,
    options: PaginationOptionsDto,
  ): Promise<CommentPaginationResultDto> {
    const filterQuery: FilterQuery<CommentDocument> = { liveSessionId };

    const result = await this.paginationService.paginate(
      this.commentModel,
      filterQuery,
      options,
    );

    return {
      data: result.data.map(comment => ({
        id: comment.id,
        commentId: comment.commentId,
        message: comment.message,
        platformUserId: comment.platformUserId,
        platformUser: {
          id: comment.platformUser.id,
          userId: comment.platformUser.userId,
          nickname: comment.platformUser.nickname,
          username: comment.platformUser.uniqueId || '',
          hasCanceledOrder: comment.platformUser.hasCanceledOrder,
          livestreamPlatform: comment.platformUser.livestreamPlatform,
          profilePictureUrl: comment.platformUser.profilePictureUrl,
          phoneNumber: comment.platformUser.phoneNumber,
          createdAt: comment.platformUser.createdAt,
        },
        createdAt: comment.createdAt,
      })),
      meta: result.meta,
    };
  }

  async findMyLiveSessionCommentsByLiveSessionIdWithCursor(
    liveSessionId: string,
    options: CursorPaginationOptionsDto,
  ): Promise<CommentCursorPaginationResultDto> {
    const filterQuery: FilterQuery<CommentDocument> = { liveSessionId };

    const result = await this.cursorPaginationService.paginate(
      this.commentModel,
      filterQuery,
      options,
      {
        cursorField: 'createdAt',
        sortOrder: 'desc', // Most recent comments first
        populateFields: ['platformUser'],
      },
    );

    return {
      data: result.data.map(comment => ({
        id: comment.id,
        commentId: comment.commentId,
        message: comment.message,
        platformUserId: comment.platformUserId,
        platformUser: {
          id: comment.platformUser.id,
          userId: comment.platformUser.userId,
          nickname: comment.platformUser.nickname,
          username: comment.platformUser.uniqueId || '',
          hasCanceledOrder: comment.platformUser.hasCanceledOrder,
          livestreamPlatform: comment.platformUser.livestreamPlatform,
          profilePictureUrl: comment.platformUser.profilePictureUrl,
          phoneNumber: comment.platformUser.phoneNumber,
          createdAt: comment.platformUser.createdAt,
        },
        createdAt: comment.createdAt,
      })),
      meta: result.meta,
    };
  }

  async getCommentsByLiveSessionId(
    roomId: string,
    ownerId?: string,
  ): Promise<CommentDocument[]> {
    const comments = await this.commentModel
      .find({ roomId })
      .populate('platformUser')
      .limit(100)
      .sort({ createdAt: -1 });

    return comments.reverse();
  }
}

import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LiveSession, LiveSessionSchema } from '@common/schemas';
import { UserModule } from '@module/user/user.module';
import { LiveSessionService } from '@module/live-session/live-session.service';
import { SchemaCollectionName } from '@common/constants/schema';
import { Comment, CommentSchema } from '@common/schemas/comment.schema';
import { SharedModule } from '@shared/shared.module';
import { LiveSessionController } from '@module/live-session/live-session.controller';
import {
  SystemUserPlatformChannel,
  SystemUserPlatformChannelSchema,
} from '@common/schemas/system-user-platform-channel.schema';

@Module({
  imports: [
    LoggerModule,
    SharedModule,
    MongooseModule.forFeature([
      {
        name: LiveSession.name,
        schema: LiveSessionSchema,
        collection: SchemaCollectionName.LiveSession,
      },
      {
        name: SystemUserPlatformChannel.name,
        schema: SystemUserPlatformChannelSchema,
        collection: SchemaCollectionName.SystemUserPlatformChannel,
      },
      {
        name: Comment.name,
        schema: CommentSchema,
        collection: SchemaCollectionName.Comment,
      },
    ]),
    UserModule,
  ],
  controllers: [LiveSessionController],
  providers: [LiveSessionService],
  exports: [LiveSessionService],
})
export class LiveSessionModule {}

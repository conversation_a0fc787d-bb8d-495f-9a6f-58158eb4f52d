import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { LivestreamPlatform } from '@common/constants';

export class LiveSessionDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  roomId: string;

  @ApiProperty()
  @Expose()
  roomTitle: string;

  @ApiProperty()
  @Expose()
  shareLink: string;

  @ApiProperty({ enum: LivestreamPlatform, enumName: 'LivestreamPlatform' })
  @Expose()
  livestreamPlatform: LivestreamPlatform;

  @ApiProperty()
  @Expose()
  platformChannelId: string;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  constructor(partial: Partial<LiveSessionDto>) {
    Object.assign(this, partial);
  }
}

import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { LivestreamPlatform } from '@common/constants';

export class GetLiveSessionDto {
  @ApiPropertyOptional({ enum: LivestreamPlatform, nullable: true })
  @IsOptional()
  @IsEnum(LivestreamPlatform)
  livestreamPlatform: LivestreamPlatform;

  @ApiPropertyOptional({ type: String, nullable: true })
  @IsOptional()
  @IsString()
  search: string;

  @ApiPropertyOptional({ type: String, nullable: true })
  @IsOptional()
  @IsString()
  platformChannelId: string;
}

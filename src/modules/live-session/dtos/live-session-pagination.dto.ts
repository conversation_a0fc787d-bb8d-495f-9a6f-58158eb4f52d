import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { PlatformChannelDto } from '@module/platform-channel/dtos/platform-channel.dto';
import { LiveSessionDto } from '@module/live-session/dtos/live-session.dto';

export class LiveSessionPaginationResultDto extends PaginationResultDto<LiveSessionDto> {
  @ApiProperty({ type: () => [LiveSessionDto] })
  data: LiveSessionDto[];

  @ApiProperty({ type: () => PaginationMetaDto })
  meta: PaginationMetaDto;
}

import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SchemaCollectionName } from '@common/constants/schema';
import { Comment, CommentSchema } from '@common/schemas/comment.schema';
import { CommentService } from '@module/comment/comment.service';

@Module({
  imports: [
    LoggerModule,
    MongooseModule.forFeature([
      {
        name: Comment.name,
        schema: CommentSchema,
        collection: SchemaCollectionName.Comment,
      },
    ]),
  ],
  providers: [CommentService],
  exports: [CommentService],
})
export class CommentModule {}

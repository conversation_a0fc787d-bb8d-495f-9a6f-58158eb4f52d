import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
  CursorPaginationMetaDto,
  CursorPaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { CommentDto } from '@module/comment/dtos/comment.dto';

export class CommentPaginationResultDto extends PaginationResultDto<CommentDto> {
  @ApiProperty({ type: () => [CommentDto] })
  data: CommentDto[];

  @ApiProperty({ type: () => PaginationMetaDto })
  meta: PaginationMetaDto;
}

export class CommentCursorPaginationResultDto extends CursorPaginationResultDto<CommentDto> {
  @ApiProperty({ type: () => [CommentDto] })
  data: CommentDto[];

  @ApiProperty({ type: () => CursorPaginationMetaDto })
  meta: CursorPaginationMetaDto;
}

import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { CommentDto } from '@module/comment/dtos/comment.dto';

export class CommentPaginationResultDto extends PaginationResultDto<CommentDto> {
  @ApiProperty({ type: () => [CommentDto] })
  data: CommentDto[];

  @ApiProperty({ type: () => PaginationMetaDto })
  meta: PaginationMetaDto;
}

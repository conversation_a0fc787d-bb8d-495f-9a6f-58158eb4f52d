import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { PlatformUserDto } from '@module/platform-user/dtos/platform-user.dto';

export class CommentDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  commentId: string;

  @ApiProperty()
  @Expose()
  message: string;

  @ApiProperty()
  @Expose()
  platformUserId: string;

  @ApiProperty({ type: () => PlatformUserDto })
  @Expose()
  platformUser: PlatformUserDto;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  constructor(partial: Partial<CommentDto>) {
    Object.assign(this, partial);
  }
}

import { AppLogger } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Comment, CommentDocument } from '@common/schemas/comment.schema';

@Injectable()
export class CommentService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(Comment.name)
    private commentModel: Model<CommentDocument>,
  ) {
    this.logger.setContext(CommentService.name);
  }

  async addCommentToLive(data: {
    commentId: string;
    message: string;
    liveSessionId: string;
    roomId: string;
    platformUserId?: string;
  }): Promise<Comment> {
    const newComment = await this.commentModel.findOneAndUpdate(
      {
        commentId: data.commentId,
        liveSessionId: data.liveSessionId,
        roomId: data.roomId,
      },
      {
        $set: {
          message: data.message,
        },
        $setOnInsert: {
          commentId: data.commentId,
          message: data.message,
          liveSessionId: data.liveSessionId,
          roomId: data.roomId,
          platformUserId: data.platformUserId,
        },
      },
      {
        upsert: true,
        new: true,
      },
    );

    try {
      return await newComment.save();
    } catch (error) {
      this.logger.error('Error saving comment to live session', error);
      throw error;
    }
  }
}

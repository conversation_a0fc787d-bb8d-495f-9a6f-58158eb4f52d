import { AppLogger } from '@common/logger/logger.service';
import {
  ConflictException,
  Injectable,
  NotFoundException,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Subscription,
  SubscriptionDocument,
  SubscriptionPlan,
  SystemUser,
  SystemUserDocument,
} from '@common/schemas';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { PaginationService } from '@shared/services/pagination.service';
import { CreateSubscriptionDto } from './dtos/create-subscription.dto';
import { UpdateSubscriptionDto } from './dtos/update-subscription.dto';
import { SubscriptionDto } from './dtos/subscription.dto';
import { SubscriptionPaginationResultDto } from './dtos/subscription-pagination.dto';
import { UserService } from '@module/user/user.service';
import { UserDto } from '@module/user/dtos/user.dto';
import { LiveSessionTrackingService } from '@module/live-comment/live-session-tracking.service';
import { UserUsageDto } from './dtos/user-usage.dto';
import { ResetUsageDto } from './dtos/reset-usage.dto';
import * as dayjs from 'dayjs';
import { StreamHubRedisWatcher } from '@module/stream-hub/watchers/providers/redis/stream-hub-redis.watcher';

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
    @InjectModel(SystemUser.name)
    private readonly systemUserModel: Model<SystemUserDocument>,
    private readonly paginationService: PaginationService,
    private readonly userService: UserService,
    @Inject(forwardRef(() => LiveSessionTrackingService))
    private readonly liveSessionTrackingService: LiveSessionTrackingService,
    private readonly streamHubRedisWatcher: StreamHubRedisWatcher,
  ) {
    this.logger.setContext(SubscriptionService.name);
  }

  async findOne(id: string): Promise<SubscriptionDto> {
    const subscription = await this.subscriptionModel.findOne({ id }).exec();

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return this.toDto(subscription);
  }

  /**
   * Find a subscription by plan
   * @param plan The subscription plan to find
   * @returns The subscription DTO or null if not found
   */
  async findByPlan(plan: SubscriptionPlan): Promise<SubscriptionDto | null> {
    const subscription = await this.subscriptionModel.findOne({ plan }).exec();

    if (!subscription) {
      return null;
    }

    return this.toDto(subscription);
  }

  async paginate(
    options: PaginationOptionsDto,
  ): Promise<SubscriptionPaginationResultDto> {
    const result = await this.paginationService.paginate(
      this.subscriptionModel,
      {},
      options,
    );

    const subscriptionsWithCreator = await Promise.all(
      result.data.map(async subscription => {
        return this.toDto(subscription);
      }),
    );

    return {
      data: subscriptionsWithCreator,
      meta: result.meta,
    };
  }

  async create(
    data: CreateSubscriptionDto,
    createdById: string,
  ): Promise<SubscriptionDto> {
    const existsSubscription = await this.subscriptionModel.findOne({
      plan: data.plan,
    });

    if (existsSubscription) {
      throw new ConflictException(
        `Subscription plan ${data.plan} already exists`,
      );
    }

    const newSubscription = await this.subscriptionModel.create({
      ...data,
      createdBy: createdById,
    });

    return this.toDto(newSubscription);
  }

  async update(
    id: string,
    data: UpdateSubscriptionDto,
  ): Promise<SubscriptionDto> {
    const subscription = await this.subscriptionModel.findOneAndUpdate(
      { id },
      { ...data, updatedAt: new Date() },
      { new: true },
    );

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return this.toDto(subscription);
  }

  async delete(id: string): Promise<SubscriptionDto> {
    // First, remove this subscription from any users who have it
    await this.systemUserModel.updateMany(
      { subscriptionId: id },
      { $unset: { subscriptionId: 1 } },
    );

    const subscription = await this.subscriptionModel.findOneAndDelete({ id });

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return this.toDto(subscription);
  }

  async assignSubscriptionToUser(
    userId: string,
    subscriptionId: string,
  ): Promise<void> {
    // Verify subscription exists
    const subscription = await this.subscriptionModel.findOne({
      id: subscriptionId,
    });
    if (!subscription) {
      throw new NotFoundException(
        `Subscription with ID ${subscriptionId} not found`,
      );
    }

    // Update user with subscription
    const updatedUser = await this.systemUserModel.findOneAndUpdate(
      { id: userId },
      { subscriptionId },
      { new: true },
    );

    if (!updatedUser) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }
  }

  async removeSubscriptionFromUser(userId: string): Promise<void> {
    const updatedUser = await this.systemUserModel.findOneAndUpdate(
      { id: userId },
      { $unset: { subscriptionId: 1 } },
      { new: true },
    );

    if (!updatedUser) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }
  }

  async getUserSubscription(userId: string): Promise<SubscriptionDto | null> {
    const user = await this.systemUserModel.findOne({ id: userId }).exec();

    if (!user || !user.subscriptionId) {
      return null;
    }

    const subscription = await this.subscriptionModel
      .findOne({
        id: user.subscriptionId,
      })
      .exec();

    if (!subscription) {
      return null;
    }

    return new SubscriptionDto({
      id: subscription.id,
      name: subscription.name,
      description: subscription.description,
      plan: subscription.plan,
      privileges: subscription.privileges,
      createdBy: undefined,
    });
  }

  async getUsersBySubscription(subscriptionId: string): Promise<UserDto[]> {
    // First verify the subscription exists
    const subscription = await this.subscriptionModel
      .findOne({ id: subscriptionId })
      .exec();

    if (!subscription) {
      throw new NotFoundException(
        `Subscription with ID ${subscriptionId} not found`,
      );
    }

    // Find all users with this subscription
    const users = await this.systemUserModel.find({ subscriptionId }).exec();

    // Convert to DTOs
    return users.map(user => this.userService.toDto(user));
  }

  private async toDto(
    subscription: SubscriptionDocument,
  ): Promise<SubscriptionDto> {
    const creator = await this.userService.findOne(subscription.createdBy);

    return new SubscriptionDto({
      id: subscription.id,
      name: subscription.name,
      description: subscription.description,
      plan: subscription.plan,
      privileges: subscription.privileges,
      createdBy: creator,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt,
    });
  }

  /**
   * Get daily usage statistics for a specific user
   * @param userId The user ID to check
   * @param date Optional date in YYYY-MM-DD format (defaults to today)
   * @returns Usage statistics for the specified date
   */
  async getUserDailyUsage(
    userId: string,
    date?: string,
  ): Promise<UserUsageDto> {
    // Validate user exists
    const user = await this.systemUserModel.findOne({ id: userId }).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get the date (default to today)
    const usageDate = date || dayjs().format('YYYY-MM-DD');

    // Get user's subscription
    const subscription = await this.subscriptionModel
      .findOne({ id: user.subscriptionId })
      .exec();

    // Get usage for the specified date
    const minutesUsed = await this.streamHubRedisWatcher.getDailyUsage(userId);

    // Get limit information
    let limit = null;
    let remaining = null;
    let isUnlimited = true;

    if (subscription) {
      const durationPrivilege = subscription.privileges.find(
        p => p.key === 'live_stream_duration',
      );

      if (durationPrivilege) {
        isUnlimited = durationPrivilege.unlimited;
        if (!isUnlimited) {
          limit = durationPrivilege.limit;
          remaining = Math.max(0, limit - minutesUsed);
        }
      }
    }

    return new UserUsageDto({
      userId,
      date: usageDate,
      minutesUsed,
      limit,
      remaining,
      isUnlimited,
    });
  }

  /**
   * Reset the minutes used by a user for a specific date
   * @param userId The user ID to reset
   * @param date Optional date in YYYY-MM-DD format (defaults to today)
   * @returns Result of the reset operation
   */
  async resetUserMinutesUsed(
    userId: string,
    date?: string,
  ): Promise<ResetUsageDto> {
    // Validate user exists
    const user = await this.systemUserModel.findOne({ id: userId }).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get the date (default to today)
    const resetDate = date || dayjs().format('YYYY-MM-DD');

    // Get current usage before reset
    const redisKey = `user:${userId}:stream:duration:daily:${resetDate}`;
    const previousUsage = parseInt(
      (await this.liveSessionTrackingService['redisService'].get(redisKey)) ||
        '0',
    );

    try {
      // Reset the usage to 0
      await this.liveSessionTrackingService['redisService'].set(redisKey, '0');

      this.logger.log(
        `Reset minutes used for user ${userId} on ${resetDate} from ${previousUsage} to 0`,
      );

      return new ResetUsageDto({
        userId,
        date: resetDate,
        previousUsage,
        success: true,
        message: `Successfully reset minutes used from ${previousUsage} to 0`,
      });
    } catch (error) {
      this.logger.error(
        `Error resetting minutes used for user ${userId}: ${error.message}`,
        error.stack,
      );

      return new ResetUsageDto({
        userId,
        date: resetDate,
        previousUsage,
        success: false,
        message: `Failed to reset minutes used: ${error.message}`,
      });
    }
  }

  async resetTrialDaysUsed(userId: string): Promise<boolean> {
    const user = await this.systemUserModel.findOne({ id: userId }).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    await this.systemUserModel.findOneAndUpdate(
      { id: userId },
      { activeAt: new Date() },
      { new: true },
    );

    return true;
  }
}

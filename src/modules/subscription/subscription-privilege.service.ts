import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Subscription,
  SubscriptionDocument,
  SubscriptionPlan,
  SubscriptionPrivilege,
  SubscriptionPrivilegeKey,
} from '@common/schemas';
import { AppLogger } from '@common/logger/logger.service';

@Injectable()
export class SubscriptionPrivilegeService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
  ) {
    this.logger.setContext(SubscriptionPrivilegeService.name);
  }

  /**
   * Get all privileges for a specific subscription plan
   */
  async getPrivilegesByPlan(
    plan: SubscriptionPlan,
  ): Promise<SubscriptionPrivilege[]> {
    const subscription = await this.subscriptionModel.findOne({ plan }).exec();

    if (!subscription) {
      throw new NotFoundException(`Subscription plan ${plan} not found`);
    }

    return subscription.privileges;
  }

  /**
   * Get a specific privilege for a subscription plan
   */
  async getPrivilegeByPlanAndKey(
    plan: SubscriptionPlan,
    key: SubscriptionPrivilegeKey,
  ): Promise<SubscriptionPrivilege | null> {
    const privileges = await this.getPrivilegesByPlan(plan);
    return privileges.find(privilege => privilege.key === key) || null;
  }

  /**
   * Get a specific privilege from a subscription object
   * Use this when you already have the subscription data
   */
  getPrivilegeByKey(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
  ): SubscriptionPrivilege | null {
    return (
      subscription.privileges.find(privilege => privilege.key === key) || null
    );
  }

  /**
   * Check if a specific privilege is unlimited for a subscription plan
   */
  async isPrivilegeUnlimited(
    plan: SubscriptionPlan,
    key: SubscriptionPrivilegeKey,
  ): Promise<boolean> {
    const privilege = await this.getPrivilegeByPlanAndKey(plan, key);
    return privilege ? privilege.unlimited : false;
  }

  /**
   * Check if a specific privilege is unlimited using a subscription object
   * Use this when you already have the subscription data
   */
  isPrivilegeUnlimitedFromSubscription(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
  ): boolean {
    const privilege = this.getPrivilegeByKey(subscription, key);
    return privilege ? privilege.unlimited : false;
  }

  /**
   * Get the limit for a specific privilege for a subscription plan
   * Returns null if the privilege is unlimited or not found
   */
  async getPrivilegeLimit(
    plan: SubscriptionPlan,
    key: SubscriptionPrivilegeKey,
  ): Promise<number | null> {
    const privilege = await this.getPrivilegeByPlanAndKey(plan, key);

    if (!privilege) {
      return null;
    }

    return privilege.unlimited ? null : privilege.limit;
  }

  /**
   * Get the limit for a specific privilege using a subscription object
   * Returns null if the privilege is unlimited or not found
   * Use this when you already have the subscription data
   */
  getPrivilegeLimitFromSubscription(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
  ): number | null {
    const privilege = this.getPrivilegeByKey(subscription, key);

    if (!privilege) {
      return null;
    }

    return privilege.unlimited ? null : privilege.limit;
  }

  /**
   * Check if a specific usage has reached or exceeded the limit for a privilege
   * Use this when you already have the subscription data
   */
  hasReachedPrivilegeLimit(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
    currentUsage: number,
  ): boolean {
    // If unlimited, user hasn't reached limit
    if (this.isPrivilegeUnlimitedFromSubscription(subscription, key)) {
      return false;
    }

    const limit = this.getPrivilegeLimitFromSubscription(subscription, key);

    // If no limit is defined, assume user hasn't reached limit
    if (limit === null) {
      return false;
    }

    // Check if current usage has reached or exceeded the limit
    return currentUsage >= limit;
  }
}

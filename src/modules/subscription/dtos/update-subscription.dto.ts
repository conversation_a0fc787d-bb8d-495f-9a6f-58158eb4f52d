import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SubscriptionPlan } from '@common/schemas';
import { PrivilegeDto } from './create-subscription.dto';

export class UpdateSubscriptionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ enum: SubscriptionPlan })
  @IsEnum(SubscriptionPlan)
  plan: SubscriptionPlan;

  @ApiProperty({ type: [PrivilegeDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PrivilegeDto)
  privileges: PrivilegeDto[];
}

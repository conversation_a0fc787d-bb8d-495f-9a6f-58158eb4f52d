import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  SubscriptionPlan,
  SubscriptionPrivilege,
  SubscriptionPrivilegeKey,
} from '@common/schemas';

export class PrivilegeDto implements SubscriptionPrivilege {
  @ApiProperty({ enum: SubscriptionPrivilegeKey })
  @IsEnum(SubscriptionPrivilegeKey)
  @IsNotEmpty()
  key: SubscriptionPrivilegeKey;

  @ApiProperty({ nullable: true })
  @IsOptional()
  limit: number | null;

  @ApiProperty()
  @IsNotEmpty()
  unlimited: boolean;
}

export class CreateSubscriptionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ enum: SubscriptionPlan })
  @IsEnum(SubscriptionPlan)
  plan: SubscriptionPlan;

  @ApiProperty({ type: [PrivilegeDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PrivilegeDto)
  privileges: PrivilegeDto[];
}

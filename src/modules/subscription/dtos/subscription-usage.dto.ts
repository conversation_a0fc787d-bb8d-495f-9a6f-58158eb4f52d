import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO for subscription usage information
 * Can be used for various subscription metrics (minutes, channels, sessions, etc.)
 * For minutes, this represents the daily usage
 */
export class SubscriptionUsageDto {
  @ApiProperty({ description: 'Amount used by the user today' })
  @Expose()
  used: number;

  @ApiProperty({
    description: 'Total limit allowed by subscription',
    nullable: true,
  })
  @Expose()
  limit: number | null;

  @ApiProperty({
    description: 'Remaining amount available to use today',
    nullable: true,
  })
  @Expose()
  remaining: number | null;

  @ApiProperty({ description: 'Whether the user has unlimited usage' })
  @Expose()
  isUnlimited: boolean;

  constructor(partial: Partial<SubscriptionUsageDto>) {
    Object.assign(this, partial);
  }
}

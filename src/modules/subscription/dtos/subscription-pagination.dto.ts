import { ApiProperty } from '@nestjs/swagger';
import {
  PaginationMetaDto,
  PaginationResultDto,
} from '@shared/dtos/pagination.dto';
import { SubscriptionDto } from './subscription.dto';

export class SubscriptionPaginationResultDto extends PaginationResultDto<SubscriptionDto> {
  @ApiProperty({ type: [SubscriptionDto] })
  data: SubscriptionDto[];

  @ApiProperty({ type: PaginationMetaDto })
  meta: PaginationMetaDto;
}

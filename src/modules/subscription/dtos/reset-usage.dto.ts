import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO for reset usage response
 */
export class ResetUsageDto {
  @ApiProperty({ description: 'User ID' })
  @Expose()
  userId: string;

  @ApiProperty({ description: 'Date of reset (YYYY-MM-DD format)' })
  @Expose()
  date: string;

  @ApiProperty({ description: 'Previous minutes used' })
  @Expose()
  previousUsage: number;

  @ApiProperty({ description: 'Success status' })
  @Expose()
  success: boolean;

  @ApiProperty({ description: 'Message' })
  @Expose()
  message: string;

  constructor(partial: Partial<ResetUsageDto>) {
    Object.assign(this, partial);
  }
}

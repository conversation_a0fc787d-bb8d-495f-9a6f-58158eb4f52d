import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO for user subscription usage information
 */
export class UserUsageDto {
  @ApiProperty({ description: 'User ID' })
  @Expose()
  userId: string;

  @ApiProperty({ description: 'Date of usage (YYYY-MM-DD format)' })
  @Expose()
  date: string;

  @ApiProperty({ description: 'Minutes used on this date' })
  @Expose()
  minutesUsed: number;

  @ApiProperty({
    description: 'Total limit allowed by subscription',
    nullable: true,
  })
  @Expose()
  limit: number | null;

  @ApiProperty({
    description: 'Remaining minutes available',
    nullable: true,
  })
  @Expose()
  remaining: number | null;

  @ApiProperty({ description: 'Whether the user has unlimited usage' })
  @Expose()
  isUnlimited: boolean;

  constructor(partial: Partial<UserUsageDto>) {
    Object.assign(this, partial);
  }
}

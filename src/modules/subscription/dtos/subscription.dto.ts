import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { SubscriptionPlan, SubscriptionPrivilege } from '@common/schemas';
import { CreatorDto } from '@module/user/dtos/creator.dto';

export class SubscriptionDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiPropertyOptional()
  @Expose()
  description?: string;

  @ApiProperty({ enum: SubscriptionPlan })
  @Expose()
  plan: SubscriptionPlan;

  @ApiProperty({ type: [Object] })
  @Expose()
  privileges: SubscriptionPrivilege[];

  @ApiProperty({ type: CreatorDto })
  @Expose()
  @Type(() => CreatorDto)
  createdBy: CreatorDto;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;

  constructor(partial: Partial<SubscriptionDto>) {
    Object.assign(this, partial);
  }
}

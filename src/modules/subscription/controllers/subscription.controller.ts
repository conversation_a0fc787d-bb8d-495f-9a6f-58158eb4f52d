import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiForbiddenResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { SubscriptionService } from '../subscription.service';
import { SubscriptionDto } from '../dtos/subscription.dto';
import { SubscriptionUsageDto } from '../dtos/subscription-usage.dto';
import { UserSubscriptionService } from '../user-subscription.service';
import { Auth, CurrentUser } from '@common/decorators';
import { UserPayload } from '@module/user/types';

@ApiTags('Subscription')
@Controller('/subscriptions')
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly userSubscriptionService: UserSubscriptionService,
  ) {}

  @Auth()
  @Get('/my-subscription')
  @ApiOkResponse({ type: SubscriptionDto })
  getMySubscription(
    @CurrentUser() user: UserPayload,
  ): Promise<SubscriptionDto | null> {
    return this.subscriptionService.getUserSubscription(user.id);
  }

  @Auth()
  @Get('/usage/minutes')
  @ApiOperation({
    summary: 'Get user live stream minutes usage for today',
  })
  @ApiOkResponse({ type: SubscriptionUsageDto })
  @ApiForbiddenResponse({ description: 'User has no subscription' })
  async getLiveStreamMinutesUsage(
    @CurrentUser() user: UserPayload,
  ): Promise<SubscriptionUsageDto> {
    return this.userSubscriptionService.getLiveStreamMinutesUsage(user.id);
  }

  @Auth()
  @Get('/usage/trial-days')
  @ApiOperation({
    summary: 'Get user live stream minutes usage for today',
  })
  @ApiOkResponse({ type: SubscriptionUsageDto })
  @ApiForbiddenResponse({ description: 'User has no subscription' })
  async getTrialDaysUsage(
    @CurrentUser() user: UserPayload,
  ): Promise<SubscriptionUsageDto> {
    return this.userSubscriptionService.getTrialDaysUsage(user.id);
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiQuery, ApiTags } from '@nestjs/swagger';
import { SubscriptionService } from '../subscription.service';
import { PaginationOptionsDto } from '@shared/dtos/pagination.dto';
import { SubscriptionPaginationResultDto } from '../dtos/subscription-pagination.dto';
import { CreateSubscriptionDto } from '../dtos/create-subscription.dto';
import { SubscriptionDto } from '../dtos/subscription.dto';
import { UpdateSubscriptionDto } from '../dtos/update-subscription.dto';
import { AdminGuard } from '@common/guards/admin.guard';
import { CurrentUser } from '@common/decorators';
import { UserPayload } from '@module/user/types';
import { UserDto } from '@module/user/dtos/user.dto';
import { UserUsageDto } from '../dtos/user-usage.dto';
import { ResetUsageDto } from '../dtos/reset-usage.dto';
import { SubscriptionUsageDto } from '@module/subscription/dtos/subscription-usage.dto';
import { UserSubscriptionService } from '@module/subscription/user-subscription.service';

@ApiTags('Subscription management for Admin')
@Controller('/admin/subscriptions')
export class AdminSubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly userSubscriptionService: UserSubscriptionService,
  ) {}

  @UseGuards(AdminGuard)
  @Get('/')
  @ApiOkResponse({ type: SubscriptionPaginationResultDto })
  paginate(
    @Query() paginationOptions: PaginationOptionsDto,
  ): Promise<SubscriptionPaginationResultDto> {
    return this.subscriptionService.paginate(paginationOptions);
  }

  @UseGuards(AdminGuard)
  @Get('/:id')
  @ApiOkResponse({ type: SubscriptionDto })
  get(@Param('id') id: string): Promise<SubscriptionDto> {
    return this.subscriptionService.findOne(id);
  }

  @UseGuards(AdminGuard)
  @Post('/')
  @ApiOkResponse({ type: SubscriptionDto })
  create(
    @Body() data: CreateSubscriptionDto,
    @CurrentUser() user: UserPayload,
  ): Promise<SubscriptionDto> {
    return this.subscriptionService.create(data, user.id);
  }

  @UseGuards(AdminGuard)
  @Put('/:id')
  @ApiOkResponse({ type: SubscriptionDto })
  update(
    @Param('id') id: string,
    @Body() data: UpdateSubscriptionDto,
  ): Promise<SubscriptionDto> {
    return this.subscriptionService.update(id, data);
  }

  @UseGuards(AdminGuard)
  @Delete('/:id')
  @ApiOkResponse({ type: SubscriptionDto })
  delete(@Param('id') id: string): Promise<SubscriptionDto> {
    return this.subscriptionService.delete(id);
  }

  @UseGuards(AdminGuard)
  @Post('/users/:userId/assign/:subscriptionId')
  @ApiOkResponse({ description: 'Subscription assigned to user successfully' })
  assignSubscriptionToUser(
    @Param('userId') userId: string,
    @Param('subscriptionId') subscriptionId: string,
  ): Promise<void> {
    return this.subscriptionService.assignSubscriptionToUser(
      userId,
      subscriptionId,
    );
  }

  @UseGuards(AdminGuard)
  @Delete('/users/:userId/remove-subscription')
  @ApiOkResponse({ description: 'Subscription removed from user successfully' })
  removeSubscriptionFromUser(@Param('userId') userId: string): Promise<void> {
    return this.subscriptionService.removeSubscriptionFromUser(userId);
  }

  @UseGuards(AdminGuard)
  @Get('/:id/users')
  @ApiOkResponse({ type: [UserDto] })
  getUsersBySubscription(
    @Param('id') subscriptionId: string,
  ): Promise<UserDto[]> {
    return this.subscriptionService.getUsersBySubscription(subscriptionId);
  }

  @UseGuards(AdminGuard)
  @Get('/users/:userId/usage-minutes')
  @ApiOkResponse({ type: UserUsageDto })
  @ApiQuery({
    name: 'date',
    required: false,
    description: 'Date in YYYY-MM-DD format (defaults to today)',
  })
  getUserDailyUsage(
    @Param('userId') userId: string,
    @Query('date') date?: string,
  ): Promise<UserUsageDto> {
    return this.subscriptionService.getUserDailyUsage(userId, date);
  }

  @UseGuards(AdminGuard)
  @Get('/users/:userId/usage-trial-days')
  @ApiOkResponse({ type: SubscriptionUsageDto })
  getUserTrialDays(
    @Param('userId') userId: string,
  ): Promise<SubscriptionUsageDto> {
    return this.userSubscriptionService.getTrialDaysUsage(userId);
  }

  @UseGuards(AdminGuard)
  @Post('/users/:userId/reset-usage-minutes')
  @ApiOkResponse({ type: ResetUsageDto })
  @ApiQuery({
    name: 'date',
    required: false,
    description: 'Date in YYYY-MM-DD format (defaults to today)',
  })
  resetUserMinutesUsed(
    @Param('userId') userId: string,
    @Query('date') date?: string,
  ): Promise<ResetUsageDto> {
    return this.subscriptionService.resetUserMinutesUsed(userId, date);
  }

  @UseGuards(AdminGuard)
  @Post('/users/:userId/reset-usage-trial-days')
  @ApiOkResponse({ type: Boolean })
  resetUserTrialDaysUsed(@Param('userId') userId: string): Promise<boolean> {
    return this.subscriptionService.resetTrialDaysUsed(userId);
  }
}

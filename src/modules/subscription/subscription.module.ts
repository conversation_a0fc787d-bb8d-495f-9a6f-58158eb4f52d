import { LoggerModule } from '@common/logger/logger.module';
import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SchemaCollectionName } from '@common/constants/schema';
import { SharedModule } from '@shared/shared.module';
import { AdminSubscriptionController } from './controllers/admin-subscription.controller';
import { SubscriptionController } from './controllers/subscription.controller';
import { SubscriptionService } from './subscription.service';
import { SubscriptionPrivilegeService } from './subscription-privilege.service';
import { UserSubscriptionService } from './user-subscription.service';
import {
  Subscription,
  SubscriptionSchema,
  SystemUser,
  SystemUserSchema,
} from '@common/schemas';
import { AdminGuard } from '@common/guards/admin.guard';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from '@module/user/user.module';
import { LiveSessionTrackingModule } from '@module/live-comment/live-session-tracking.module';
import { StreamHubRedisWatcher } from '@module/stream-hub/watchers/providers/redis/stream-hub-redis.watcher';

@Module({
  imports: [
    JwtModule.register({}),
    LoggerModule,
    SharedModule,
    UserModule,
    forwardRef(() => LiveSessionTrackingModule),
    MongooseModule.forFeature([
      {
        name: Subscription.name,
        schema: SubscriptionSchema,
        collection: SchemaCollectionName.Subscription,
      },
      {
        name: SystemUser.name,
        schema: SystemUserSchema,
        collection: SchemaCollectionName.SystemUser,
      },
    ]),
  ],
  controllers: [AdminSubscriptionController, SubscriptionController],
  providers: [
    SubscriptionService,
    SubscriptionPrivilegeService,
    UserSubscriptionService,
    AdminGuard,
    StreamHubRedisWatcher,
  ],
  exports: [
    SubscriptionService,
    SubscriptionPrivilegeService,
    UserSubscriptionService,
  ],
})
export class SubscriptionModule {}

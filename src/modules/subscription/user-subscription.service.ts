import {
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Subscription,
  SubscriptionDocument,
  SubscriptionPrivilege,
  SubscriptionPrivilegeKey,
  SystemUser,
  SystemUserDocument,
} from '@common/schemas';
import { AppLogger } from '@common/logger/logger.service';
import { SubscriptionPrivilegeService } from './subscription-privilege.service';
import { LiveSessionTrackingService } from '@module/live-comment/live-session-tracking.service';
import { SubscriptionUsageDto } from './dtos/subscription-usage.dto';
import { StreamHubRedisWatcher } from '@module/stream-hub/watchers/providers/redis/stream-hub-redis.watcher';
import * as dayjs from 'dayjs';

@Injectable()
export class UserSubscriptionService {
  constructor(
    private readonly logger: AppLogger,
    @InjectModel(SystemUser.name)
    private readonly userModel: Model<SystemUserDocument>,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
    private readonly subscriptionPrivilegeService: SubscriptionPrivilegeService,
    @Inject(forwardRef(() => LiveSessionTrackingService))
    private readonly liveSessionTrackingService: LiveSessionTrackingService,
    private readonly streamHubRedisWatcher: StreamHubRedisWatcher,
  ) {
    this.logger.setContext(UserSubscriptionService.name);
  }

  /**
   * Get the subscription for a user
   */
  async getUserSubscription(
    userId: string,
  ): Promise<SubscriptionDocument | null> {
    const user = await this.userModel.findOne({ id: userId }).exec();

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    if (!user.subscriptionId) {
      return null;
    }

    return this.subscriptionModel.findOne({ id: user.subscriptionId }).exec();
  }

  /**
   * Get all privileges for a user's subscription
   */
  async getUserPrivileges(userId: string): Promise<SubscriptionPrivilege[]> {
    const subscription = await this.getUserSubscription(userId);

    if (!subscription) {
      // If user has no subscription, return empty array
      return [];
    }

    return subscription.privileges;
  }

  /**
   * Get a specific privilege for a user
   */
  async getUserPrivilegeByKey(
    userId: string,
    key: SubscriptionPrivilegeKey,
  ): Promise<SubscriptionPrivilege | null> {
    const privileges = await this.getUserPrivileges(userId);
    return privileges.find(privilege => privilege.key === key) || null;
  }

  /**
   * Check if a specific privilege is unlimited for a user
   */
  async isUserPrivilegeUnlimited(
    userId: string,
    key: SubscriptionPrivilegeKey,
  ): Promise<boolean> {
    const privilege = await this.getUserPrivilegeByKey(userId, key);
    return privilege ? privilege.unlimited : false;
  }

  /**
   * Get the limit for a specific privilege for a user
   * Returns null if the privilege is unlimited or not found
   */
  async getUserPrivilegeLimit(
    userId: string,
    key: SubscriptionPrivilegeKey,
  ): Promise<number | null> {
    const privilege = await this.getUserPrivilegeByKey(userId, key);

    if (!privilege) {
      return null;
    }

    return privilege.unlimited ? null : privilege.limit;
  }

  /**
   * Check if a user has reached their limit for a specific privilege
   * @param userId The user ID
   * @param key The privilege key
   * @param currentUsage The current usage to check against the limit
   * @returns true if the user has reached or exceeded their limit, false otherwise
   */
  async hasUserReachedPrivilegeLimit(
    userId: string,
    key: SubscriptionPrivilegeKey,
    currentUsage: number,
  ): Promise<boolean> {
    const subscription = await this.getUserSubscription(userId);

    if (!subscription) {
      // If user has no subscription, assume they've reached the limit
      return true;
    }

    return this.subscriptionPrivilegeService.hasReachedPrivilegeLimit(
      subscription,
      key,
      currentUsage,
    );
  }

  /**
   * Get all privileges from a user document that already has subscription data
   * Use this when you already have the user with populated subscription data
   */
  getUserPrivilegesFromSubscription(
    subscription: SubscriptionDocument,
  ): SubscriptionPrivilege[] {
    if (!subscription) {
      return [];
    }

    return subscription.privileges;
  }

  /**
   * Get a specific privilege from a subscription document
   * Use this when you already have the subscription data
   */
  getUserPrivilegeByKeyFromSubscription(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
  ): SubscriptionPrivilege | null {
    if (!subscription) {
      return null;
    }

    return this.subscriptionPrivilegeService.getPrivilegeByKey(
      subscription,
      key,
    );
  }

  /**
   * Check if a specific privilege is unlimited using pre-fetched subscription data
   * Use this when you already have the subscription data
   */
  isUserPrivilegeUnlimitedFromSubscription(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
  ): boolean {
    if (!subscription) {
      return false;
    }

    return this.subscriptionPrivilegeService.isPrivilegeUnlimitedFromSubscription(
      subscription,
      key,
    );
  }

  /**
   * Get the limit for a specific privilege using pre-fetched subscription data
   * Returns null if the privilege is unlimited or not found
   * Use this when you already have the subscription data
   */
  getUserPrivilegeLimitFromSubscription(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
  ): number | null {
    if (!subscription) {
      return null;
    }

    return this.subscriptionPrivilegeService.getPrivilegeLimitFromSubscription(
      subscription,
      key,
    );
  }

  /**
   * Check if a user has reached their limit for a specific privilege using pre-fetched subscription data
   * Use this when you already have the subscription data
   */
  hasUserReachedPrivilegeLimitFromSubscription(
    subscription: SubscriptionDocument,
    key: SubscriptionPrivilegeKey,
    currentUsage: number,
  ): boolean {
    if (!subscription) {
      // If no subscription, assume they've reached the limit
      return true;
    }

    return this.subscriptionPrivilegeService.hasReachedPrivilegeLimit(
      subscription,
      key,
      currentUsage,
    );
  }

  /**
   * Get live stream minutes usage information for a user
   * @param userId The user ID to check
   * @returns DTO with usage information
   * @throws ForbiddenException if user has no subscription
   */
  async getLiveStreamMinutesUsage(
    userId: string,
  ): Promise<SubscriptionUsageDto> {
    // Get user's subscription
    const subscription = await this.getUserSubscription(userId);

    if (!subscription) {
      throw new ForbiddenException(
        'Subscription required to access usage statistics',
      );
    }

    // Find the LIVE_STREAM_DURATION privilege
    const durationPrivilege = this.getUserPrivilegeByKeyFromSubscription(
      subscription,
      SubscriptionPrivilegeKey.LIVE_STREAM_DURATION,
    );

    if (!durationPrivilege) {
      throw new InternalServerErrorException(
        'Subscription does not have live stream duration configuration',
      );
    }

    // Get total usage data from tracking service
    const usedMinutes = await this.streamHubRedisWatcher.getDailyUsage(userId);

    // Get the limit (null if unlimited)
    const limit = durationPrivilege.unlimited ? null : durationPrivilege.limit;

    // For limited subscriptions, ensure used doesn't exceed limit in the response
    let used = usedMinutes;
    if (limit !== null && used > limit) {
      this.logger.warn(
        `User ${userId} has used ${used} minutes, which exceeds their limit of ${limit} minutes`,
      );
      used = limit; // Cap the used value at the limit for the response
    }

    // Calculate remaining (always at least 0)
    const remaining = durationPrivilege.unlimited
      ? null
      : Math.max(0, (limit as number) - usedMinutes);

    // Prepare response
    return new SubscriptionUsageDto({
      used,
      limit,
      remaining,
      isUnlimited: durationPrivilege.unlimited,
    });
  }

  async getTrialDaysUsage(userId: string): Promise<SubscriptionUsageDto> {
    // Get user's subscription
    const subscription = await this.getUserSubscription(userId);

    if (!subscription) {
      throw new ForbiddenException(
        'Subscription required to access usage statistics',
      );
    }

    // Find the LIVE_STREAM_DURATION privilege
    const durationPrivilege = this.getUserPrivilegeByKeyFromSubscription(
      subscription,
      SubscriptionPrivilegeKey.TRIAL_DAYS,
    );

    if (!durationPrivilege) {
      throw new InternalServerErrorException(
        'Subscription does not have live stream duration configuration',
      );
    }

    const systemUser = await this.userModel.findOne({
      id: userId,
    });

    // Get total usage data from tracking service
    const used = dayjs().diff(dayjs(systemUser.activeAt), 'day');

    // Get the limit (null if unlimited)
    const limit = durationPrivilege.unlimited ? null : durationPrivilege.limit;

    // Calculate remaining (always at least 0)
    const remaining = durationPrivilege.unlimited
      ? null
      : Math.max(0, (limit as number) - used);

    // Prepare response
    return new SubscriptionUsageDto({
      used,
      limit,
      remaining,
      isUnlimited: durationPrivilege.unlimited,
    });
  }
}

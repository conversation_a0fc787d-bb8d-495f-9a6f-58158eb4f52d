import axios, { Axios } from 'axios';
import { FACEBOOK_HEADERS } from './constants/facebook-headers';
import { HEADLESS_SERVER } from '@common/config/enviroment.config';

export class FacebookClient {
  protected http: Axios;

  constructor() {
    this.http = new Axios({
      headers: FACEBOOK_HEADERS,
    });
  }

  async getCurrentLiveIdByPageUrl(pageUrl: string): Promise<string | null> {
    const { data } = await this.http.get(pageUrl);

    const regex2 = /"live_video_for_comet_live_ring":{"id":"(\d{16})"/;
    const match2 = data.match(regex2);

    if (match2 && match2[1]) {
      return match2[1];
    }

    const regex = /video_id\\":\\"([0-9]+)\\"/;

    const match = data.match(regex);

    if (match && match[1]) {
      return match[1];
    }

    try {
      const { data: pageData } = await axios.get(
        `${HEADLESS_SERVER}/get-facebook-live-id?url=${encodeURIComponent(
          pageUrl,
        )}`,
      );

      if (pageData?.liveId) {
        return pageData.liveId;
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}

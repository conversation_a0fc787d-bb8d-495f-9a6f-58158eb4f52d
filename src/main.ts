// Ensure crypto is available globally
import * as crypto from 'crypto';

// Only set global.crypto if it's not already defined
if (typeof global.crypto === 'undefined') {
  (global as any).crypto = crypto;
}

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { RequestIdMiddleware } from '@common/middlewares/request-id.middleware';
import { ConfigService } from '@nestjs/config';
import { AppExceptionsFilter } from '@common/filters/app-exceptions.filter';
import { LoggingInterceptor } from '@common/interceptors/logging.interceptor';
import { HttpStatus, ValidationPipe } from '@nestjs/common';
import { Swagger } from '@common/config';
import { AppLogger } from '@common/logger/logger.service';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: new AppLogger(),
  });
  const logger = app.get(AppLogger);

  app.useLogger(logger);
  app.use(RequestIdMiddleware);
  app.useGlobalFilters(new AppExceptionsFilter(logger));
  app.useGlobalInterceptors(new LoggingInterceptor(logger));
  app.useGlobalPipes(
    new ValidationPipe({
      errorHttpStatusCode: HttpStatus.PRECONDITION_FAILED,
      transform: true,
    }),
  );
  app.enableCors();
  app.setGlobalPrefix('api');
  Swagger.initSwagger(app);

  const configService = app.get(ConfigService);
  const port = configService.get('PORT');

  logger.log(`App listening at ${port}`);
  await app.listen(port || 3000);
}
bootstrap();

// Import your schemas here
import type { Connection } from 'mongoose';

export async function up(connection: Connection): Promise<void> {
  const collections = await connection.db.listCollections().toArray();
  let tempedLiveSessions = false;
  let tempedPlatformUsers = false;

  if (collections.some(collection => collection.name === 'live_sessions')) {
    await connection.collection('live_sessions').rename('temp_live_sessions');
    tempedLiveSessions = true;
  }

  if (
    collections.some(collection => collection.name === 'tiktok_live_sessions')
  ) {
    await connection.collection('tiktok_live_sessions').rename('live_sessions');
    await connection.collection('tiktok_live_sessions').drop();
    if (tempedLiveSessions) {
      await connection.collection('temp_live_sessions').drop();
    }
  }

  if (collections.some(collection => collection.name === 'platform_users')) {
    await connection.collection('platform_users').rename('temp_platform_users');
    tempedPlatformUsers = true;
  }

  if (collections.some(collection => collection.name === 'tiktok_users')) {
    await connection.collection('tiktok_users').rename('platform_users');
    await connection.collection('tiktok_users').drop();
    if (tempedPlatformUsers) {
      await connection.collection('temp_platform_users').drop();
    }
  }
}

export async function down(connection: Connection): Promise<void> {
  //
}

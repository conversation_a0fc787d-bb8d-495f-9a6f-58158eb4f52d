const webpack = require('webpack');
const path = require('path');

// This function is used when webpack is enabled in nest-cli.json
module.exports = function (options) {
  // Only apply these changes in production mode
  if (process.env.NODE_ENV === 'production') {
    return {
      ...options,
      plugins: [
        ...(options.plugins || []),
        new webpack.ProvidePlugin({
          // Add Node.js core modules here
          crypto: 'crypto',
        }),
      ],
      resolve: {
        ...(options.resolve || {}),
        alias: {
          ...(options.resolve?.alias || {}),
          '@common': path.resolve(__dirname, 'src/common'),
          '@module': path.resolve(__dirname, 'src/modules'),
          '@shared': path.resolve(__dirname, 'src/shared'),
          '@helpers': path.resolve(__dirname, 'src/helpers'),
          '@infras': path.resolve(__dirname, 'src/infras'),
        },
      },
    };
  }

  // In development mode, return the original options
  return options;
};

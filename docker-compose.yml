version: '3.8'

services:
  live_server:
    platform: linux/amd64
    image: mdukkdo/tiktok_sign:latest
    ports:
      - '8080:8080'
    networks:
      - live_network
    restart: always

  live_headless:
    platform: linux/amd64
    image: scoris/live-headless:dev
    ports:
      - '4005:4005'
    depends_on:
      live_manager:
        condition: service_healthy
    networks:
      - live_network
    restart: always

  live_manager:
    platform: linux/amd64
    image: scoris/live-manager:prod
    ports:
      - '3000:3000'
    depends_on:
      - live_server
      - mongo
    networks:
      - live_network
    restart: always
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/api/health" ]
      interval: 10s
      timeout: 5s
      retries: 3

  mongo:
    image: mongo:latest
    ports:
      - '27017:27017'
    volumes:
      - mongo_data:/data/db
    networks:
      - live_network

volumes:
  mongo_data:

networks:
  live_network:
    driver: bridge
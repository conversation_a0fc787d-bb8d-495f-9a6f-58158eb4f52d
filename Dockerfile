# Base image
FROM node:18 AS build

WORKDIR /app

# Copy package.json and install dependencies in a single layer to optimize cache usage
COPY package*.json ./
RUN npm install -g pnpm
RUN pnpm install

# Copy the rest of the source code and build the project in a single layer
COPY . .
RUN pnpm run build:prod

# Production image
FROM node:18-slim

RUN apt-get update && apt-get install curl -y

WORKDIR /app

# Copy only necessary files from build stage
COPY --from=build /app/package*.json ./
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/dist ./dist

EXPOSE 3000
CMD ["npm", "run", "start:prod"]

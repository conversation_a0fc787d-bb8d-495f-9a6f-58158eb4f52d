{"name": "otiktok-live-comment", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "build:prod": "NODE_ENV=production nest build --webpack", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "postinstall": "husky install && patch-package", "migration:create": "yarn run migrate create", "migration:up": "yarn run migrate up", "migration:down": "yarn run migrate down", "migration:prune": "yarn run migrate prune"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.ts": ["npm run lint", "npm run format", "git add ."]}, "dependencies": {"@logtail/node": "^0.5.4", "@logtail/winston": "^0.5.4", "@milahu/patch-package": "^6.4.14", "@nestjs/axios": "^2.0.0", "@nestjs/common": "^9.4.3", "@nestjs/config": "^2.3.4", "@nestjs/core": "^9.4.3", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.1.0", "@nestjs/platform-express": "^9.4.3", "@nestjs/platform-socket.io": "^10.4.17", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^6.3.0", "@nestjs/websockets": "^10.4.17", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.40.3", "@opentelemetry/exporter-trace-otlp-grpc": "^0.53.0", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-node": "^0.53.0", "@opentelemetry/semantic-conventions": "^1.32.0", "@socket.io/redis-adapter": "^8.3.0", "@vulinhscoris/tiktok-live-connector": "^1.3.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^5.1.0", "ioredis": "^5.6.1", "lodash": "^4.17.21", "masterchat": "^1.1.0", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "nest-winston": "^1.10.2", "nestjs-otel": "^5.1.5", "redis": "^4.7.0", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "ts-migrate-mongoose": "^3.8.9", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1", "youtube-chat": "^2.2.0", "youtubei": "^1.6.7"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.21", "@types/jest": "29.5.1", "@types/lodash": "^4.17.16", "@types/node": "18.16.12", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.1", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^9.1.7", "jest": "29.5.0", "lint-staged": "^15.5.1", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "29.1.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}
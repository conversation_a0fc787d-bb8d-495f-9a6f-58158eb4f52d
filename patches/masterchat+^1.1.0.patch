# generated by patch-package 6.4.14
#
# declared package:
#   masterchat: ^1.1.0
#
diff --git a/node_modules/masterchat/lib/masterchat.js b/node_modules/masterchat/lib/masterchat.js
index 47c3ec6..1d4eeba 100644
--- a/node_modules/masterchat/lib/masterchat.js
+++ b/node_modules/masterchat/lib/masterchat.js
@@ -2926,7 +2926,7 @@ class Masterchat extends events.exports.EventEmitter {
                 const driftMs = Date.now() - startMs;
                 const timeoutMs = continuation.timeoutMs - driftMs;
                 if (timeoutMs > 500) {
-                    await delay(timeoutMs, signal);
+                    await delay(2000);
                 }
             }
         }
